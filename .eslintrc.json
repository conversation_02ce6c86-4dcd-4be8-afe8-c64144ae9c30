{"extends": ["next/core-web-vitals", "prettier"], "plugins": ["unused-imports"], "rules": {"unused-imports/no-unused-imports": "error", "react/no-unescaped-entities": "off", "react/react-in-jsx-scope": "off", "import/no-anonymous-default-export": "off", "no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/explicit-module-boundary-types": "off"}, "settings": {"react": {"version": "detect"}}}