'use server';
import { Resend } from 'resend';
import { getHtml } from '@/app/(others)/contact/contact-inline';
import { ContactFormValidator, TContactform } from '@/schema';
import { revalidatePath } from 'next/cache';

const resend = new Resend(process.env.RESEND_API_KEY);
const from = process.env.EMAIL_FROM;
const to = process.env.EMAIL_TO;
export async function sendEmail(data: TContactform) {
  const result = ContactFormValidator.safeParse(data);

  if (result.success) {
    const { name, email, message } = result.data;
    try {
      const data = await resend.emails.send({
        from: String(from),
        to: String(to),
        subject: 'Contact form submission',
        text: `Name: ${name}\nEmail: ${email}\nMessage: ${message}`,
        html: getHtml(result.data),
      });
      return { success: true, data };
    } catch (error) {
      return { success: false, error };
    }
  }

  if (result.error) {
    return { success: false, error: result.error.format() };
  }
}

export async function revalidateServerPath(path: string) {
  revalidatePath(path);
}
