'use client';
import { useDocumentTitle } from '@mantine/hooks';
import { AnimatePresence } from 'framer-motion';
import { parseAsNumberLiteral, useQueryState } from 'nuqs';
import ChoosePack from './chose-pack';
import CheckPayment from './check-payment';
import ChoosePaymentMethod from '../checkout/ChoosePaymentMethod';
import { CHECKOUTDEALERSETP } from '@/config';

function CheckOutDealerPage() {
  useDocumentTitle('OC | Checkout Dealer');
  const [step] = useQueryState(
    'step',
    parseAsNumberLiteral(CHECKOUTDEALERSETP).withDefault(1),
  );
  return (
    <AnimatePresence mode="wait">
      <main className="relative grid min-h-[75vh] place-content-center">
        {step == 1 ? <ChoosePack /> : null}
        {step == 2 ? (
          <ChoosePaymentMethod steps={CHECKOUTDEALERSETP} prevStep={1} />
        ) : null}
        {step == 3 ? <CheckPayment /> : null}
      </main>
    </AnimatePresence>
  );
}

export default CheckOutDealerPage;
