import { motion } from 'framer-motion';
import PhoneForm from './PhoneForm';
import mtn from '@/public/momo.jpg';
import orange from '@/public/orange.jpg';
import Image from 'next/image';
import paypal from '@/public/paypal.svg';
import card from '@/public/master-card.svg';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { Suspense } from 'react';
import { v4 as uuid } from 'uuid';
import { CHECKOUTSTEPS, EXAMS, METHODS } from '@/config';
import { useSession } from 'next-auth/react';
import {
  parseAsNumberLiteral,
  parseAsStringLiteral,
  useQueryState,
} from 'nuqs';
import { useCountry } from '@/hooks/use-country';
import { useClientOffers } from '@/hooks/offers';
import CardForm from '../_components/payment-card-form';
const SRC = {
  MTN: mtn,
  ORANGE: orange,
  CARD: card,
  PAYPAL: paypal,
};
function AcceptPayment() {
  const { data: session } = useSession();
  const { data: offers } = useClientOffers();
  const [, setStep] = useQueryState(
    'step',
    parseAsNumberLiteral(CHECKOUTSTEPS).withDefault(1),
  );
  const [selectedPrice] = useQueryState('selectedPrice');

  const [examen] = useQueryState(
    'examen',
    parseAsStringLiteral(EXAMS).withDefault('TCF'),
  );
  const [method, setMethod] = useQueryState(
    'method',
    parseAsStringLiteral(METHODS).withDefault('ORANGE'),
  );
  const { dealerContry } = useCountry();
  const pricing = offers?.find((np) => np._id == selectedPrice);
  const date = new Date(Date.now());

  const currency = method != 'CARD' ? 'XAF' : 'EUR';
  const price =
    method === 'CARD'
      ? pricing?.[dealerContry].euro
      : pricing?.[dealerContry].local;
  const ref = `${uuid()}:${session?.user.email}:${date.toISOString()}`;

  const payload = {
    amount: price,
    currency,
    reason: `abonnement du ${new Date(Date.now()).toLocaleDateString()}  pour la preparation à l'examen du ${examen} au prix de ${price} ${currency}`,
    reference: ref,
    email: session?.user.email!,
    name: session?.user.email!,
    channel: `cm.${method.toLowerCase()}`,
    examen: examen,
    offre: pricing?._id,
  };
  const [callbackUrl] = useQueryState('callbackUrl');

  return (
    <motion.section
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{
        delay: 0,
        duration: 0.5,
        type: 'tween',
      }}
      className="prose relative z-20 flex min-h-full min-w-full flex-col items-center bg-white px-8 pb-3 pt-10 lg:px-0 lg:pt-[60px]"
    >
      <Button
        variant={'outline'}
        size={'sm'}
        className="absolute left-3 top-3"
        onClick={() => {
          setMethod(null);
          setStep(3);
        }}
      >
        <ArrowLeft className="h-5 w-5 text-blue-500" />
      </Button>
      <h2>Terminez votre achat</h2>
      <p>
        Remplisser le formulaire pour souscrire a l&apos;offre{' '}
        <span className="bg-gradient-to-r from-green-300 via-blue-500 to-purple-600 bg-clip-text font-bold text-transparent">
          {pricing?.title}
        </span>{' '}
        pour un montant de{' '}
        <span className="font-bold">
          {price} {currency}
        </span>
      </p>
      <div className="flex h-24 w-24 items-center justify-center overflow-hidden rounded-sm shadow-md shadow-slate-400/70">
        <Image src={SRC[method]} alt="orange-mtn-thumbnail" />
      </div>
      <div className="w-full lg:max-w-lg">
        <Suspense fallback={<Loader2 className="h-5 w-5 animate-spin" />}>
          {method == 'MTN' || method == 'ORANGE' ? (
            <PhoneForm
              method={method}
              payload={payload}
              callbackUrl={callbackUrl || undefined}
              acceptPromo
            />
          ) : null}

          {method == 'CARD' && offers ? (
            <CardForm acceptPromo offers={offers} />
          ) : null}
        </Suspense>
      </div>
    </motion.section>
  );
}

export default AcceptPayment;
