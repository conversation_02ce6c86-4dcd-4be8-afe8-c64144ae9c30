import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { CHECKOUTSTEPS, EXAMS, PACKS } from '@/config';
import { ArrowLeft } from 'lucide-react';
import {
  parseAsNumberLiteral,
  parseAsStringLiteral,
  useQueryState,
} from 'nuqs';

function ChooseExam() {
  const [, setStep] = useQueryState(
    'step',
    parseAsNumberLiteral(CHECKOUTSTEPS).withDefault(1),
  );
  const [, setPrice] = useQueryState(
    'selectedPrice',
    parseAsStringLiteral(PACKS).withDefault('APPROFONDIE'),
  );
  const [, setExamen] = useQueryState(
    'examen',
    parseAsStringLiteral(EXAMS).withDefault('TCF'),
  );
  const handleChoice = (value: string) => {
    setExamen(value as any);
    setStep(3);
  };
  return (
    <div className="flex h-full items-center justify-center">
      <Button
        variant={'outline'}
        size={'sm'}
        className="absolute left-3 top-3"
        onClick={() => {
          setPrice(null);
          setStep(1);
        }}
      >
        <ArrowLeft className="h-5 w-5 text-blue-500" />
      </Button>
      <Card>
        <CardHeader>
          <CardTitle>Examen</CardTitle>
          <CardDescription>
            Choisissez le test dont vous souhaitez faire l&apos;apprentissage.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center gap-1.5">
            <Button onClick={() => handleChoice('TCF')}>TCF</Button>
            <Button onClick={() => handleChoice('TEF')}>TEF</Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default ChooseExam;
