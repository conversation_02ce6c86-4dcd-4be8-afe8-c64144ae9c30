import React, { useRef } from 'react';
import { PRICINGS_DETAILS } from '@/constants/text';
import { NumericFormat } from 'react-number-format';
import { FaTimes } from 'react-icons/fa';
import { ImCheckboxChecked } from 'react-icons/im';
import { motion } from 'framer-motion';
import { TestModal } from './alert-test';
import Image from 'next/image';
import mtn from '@/public/momo.jpg';
import orange from '@/public/orange.jpg';
import paypal from '@/public/paypal.svg';
import card from '@/public/master-card.svg';
import { cn } from '@/lib/utils';
import { useSession } from 'next-auth/react';
import { parseAsNumberLiteral, useQueryState } from 'nuqs';
import { CHECKOUTSTEPS } from '@/config';
import { useCountry } from '@/hooks/use-country';
import { useClientOffers } from '@/hooks/offers';
import { getCurrency } from '@/lib/utils';
function ChoosePack() {
  const { data: session } = useSession();
  const { data: offers } = useClientOffers();
  const [, setStep] = useQueryState(
    'step',
    parseAsNumberLiteral(CHECKOUTSTEPS).withDefault(1),
  );
  const [, setPrice] = useQueryState('selectedPrice');
  const alert = useRef<HTMLButtonElement>(null);

  const { dealerContry } = useCountry();

  return (
    <motion.section
      initial={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -100 }}
      transition={{
        duration: 0.5,
        type: 'tween',
      }}
      className="relative z-20 overflow-hidden bg-white pb-12 pt-10 lg:pb-[90px] lg:pt-[60px]"
    >
      <div className="container">
        <div className="-mx-4 flex flex-wrap">
          <div className="relative w-full px-4">
            <motion.div
              initial={{ y: '-100%', opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{
                type: 'spring',
                duration: 1,
                stiffness: 20,
                damping: 15,
                mass: 2,
              }}
              className="mx-auto mb-24 max-w-[510px] text-center lg:mb-20"
            >
              <span className="mb-2 block text-lg font-semibold text-primary">
                Grille de prix
              </span>
              <h1 className="mx-auto mb-5 text-center text-4xl font-extrabold text-gray-900">
                Nos plans tarifaires !
              </h1>
              <h2 className="mx-auto mb-5 text-center font-sans text-2xl text-gray-900">
                Paiement{' '}
                <span className="mb-0.5 bg-gradient-to-r from-green-300 via-blue-500 to-purple-600 bg-clip-text text-3xl font-bold text-transparent">
                  rapide
                </span>{' '}
                et{' '}
                <span className="mb-0.5 bg-gradient-to-r from-green-300 via-blue-500 to-purple-600 bg-clip-text text-3xl font-bold text-transparent">
                  sécurisé.
                </span>{' '}
              </h2>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{
                type: 'spring',
                duration: 0.5,
                stiffness: 15,
                damping: 10,
                mass: 1,
              }}
              className="absolute bottom-8 flex items-center gap-1 self-center lg:right-20"
            >
              <div className="relative h-16 w-16 overflow-hidden rounded-full shadow-md shadow-slate-600/50">
                <Image fill src={mtn} alt="mtn" />
              </div>
              <div className="relative h-16 w-16 overflow-hidden rounded-full shadow-md shadow-slate-600/50">
                <Image fill src={orange} alt="mtn" />
              </div>
              <div className="relative h-16 w-16 overflow-hidden rounded-full shadow-md shadow-slate-600/50">
                <Image fill src={card} alt="mtn" />
              </div>
              <div className="relative h-16 w-16 overflow-hidden rounded-full shadow-md shadow-slate-600/50">
                <Image fill src={paypal} alt="mtn" />
              </div>
            </motion.div>
          </div>
        </div>
        <div className="grid lg:-mx-4 lg:grid-cols-3">
          {offers?.map((pricing, i) => {
            return (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{
                  type: 'spring',
                  delay: 0.5 + i,
                  duration: 0.2,
                  stiffness: 20,
                  damping: 15,
                  mass: 2,
                }}
                className={cn('grid w-full px-4', {
                  'order-6 md:order-1': pricing.title == 'FREE',
                  'order-2': pricing.title == 'PERFECTION',
                  'order-3': pricing.title == 'APPROFONDIE',
                  'order-4': pricing.title == 'INTENSE',
                  'order-5': pricing.title == 'DECOUVERTE',
                  hidden: pricing.title == 'FREE' && Boolean(session),
                })}
                key={i}
              >
                <div className="shadow-pricing relative z-10 row-[span_8] mb-10 grid grid-rows-[subgrid] overflow-hidden rounded-xl border border-primary border-opacity-20 bg-white px-8 py-10 sm:p-12 lg:px-6 lg:py-10 xl:p-12">
                  <span className="mb-4 block text-lg font-semibold text-primary">
                    {pricing.title}
                  </span>
                  <h2 className="text-dark mb-1 text-[42px] font-bold">
                    <NumericFormat
                      displayType="text"
                      allowLeadingZeros
                      renderText={(value) => <>{value}</>}
                      value={pricing?.[dealerContry].local}
                      thousandSeparator=" "
                    />{' '}
                    {getCurrency(dealerContry)}
                    {dealerContry !== 'international' ? (
                      <span className="text-body-color text-base font-medium">
                        /{' '}
                        <NumericFormat
                          displayType="text"
                          allowLeadingZeros
                          renderText={(value) => <>{value}</>}
                          value={pricing?.[dealerContry].euro}
                          thousandSeparator=" "
                        />{' '}
                        €
                      </span>
                    ) : null}
                  </h2>
                  <div>
                    {/* {pricing.discount ? (
                      <span className="mb-1 flex items-end text-gray-400">
                        <h2 className="w-fit text-[21px] font-bold line-through">
                          <NumericFormat
                            displayType="text"
                            allowLeadingZeros
                            renderText={(value) => <>{value}</>}
                            value={pricing.discount}
                            thousandSeparator=" "
                          />{' '}
                          F
                        </h2>
                      </span>
                    ) : null} */}
                  </div>
                  <p className="mb-3 text-base font-medium text-slate-800">
                    {pricing.subtitle}
                  </p>
                  <h3 className="text-md mb-2 font-bold">
                    <span className="font-normal">Validité : </span>
                    {pricing.validityDays > 0 ? (
                      <>
                        {pricing.validityDays} jours{' '}
                        {pricing.promotionDays && pricing.promotionDays > 0 ? (
                          <span className="italic text-emerald-500">
                            + {pricing.promotionDays} (bonus code promo)
                          </span>
                        ) : null}
                      </>
                    ) : (
                      <p className="italic text-emerald-500">
                        {'Nombre de jours illimités'}
                      </p>
                    )}
                  </h3>
                  <button
                    onClick={() => {
                      setStep(2);
                      setPrice(pricing._id);
                    }}
                    className="block w-full animate-buttonheartbeat rounded-md border border-[#D4DEFF] bg-primary p-4 text-center text-sm font-semibold text-white"
                  >
                    {' Payez Maintenant'}
                  </button>
                  <hr className="my-6" />
                  <div className="prose prose-base mb-7 prose-li:m-0 prose-li:mb-2 prose-li:font-normal prose-li:leading-[20px] prose-li:text-slate-900">
                    <ol className="list-outside list-disc">
                      {PRICINGS_DETAILS.map((details) => {
                        if (!details.child) {
                          return (
                            <li key={details.title}>
                              <span className="m-0 flex items-stretch gap-1">
                                <p className="m-0 w-fit">{details.title}</p>
                                {details.title.includes('WhatsApp') &&
                                (pricing.title == 'DECOUVERTE' ||
                                  pricing.title == 'INTENSE') ? (
                                  <FaTimes className="text-red-500" />
                                ) : (
                                  <ImCheckboxChecked className="text-emerald-400" />
                                )}
                              </span>
                            </li>
                          );
                        } else {
                          return (
                            <li key={details.title}>
                              {details.title}
                              <ol className="list-outside list-disc">
                                <li>
                                  <span className="m-0 flex items-center gap-1">
                                    1 sujets
                                    <ImCheckboxChecked className="text-emerald-400" />
                                  </span>
                                </li>
                                <li>
                                  <span className="m-0 flex items-center gap-1">
                                    10 sujets
                                    <ImCheckboxChecked className="text-emerald-400" />
                                  </span>{' '}
                                </li>
                                <li>
                                  <span className="m-0 flex items-center gap-1">
                                    15 sujets
                                    {pricing.title == 'DECOUVERTE' ? (
                                      <FaTimes className="text-red-500" />
                                    ) : (
                                      <ImCheckboxChecked className="text-emerald-400" />
                                    )}
                                  </span>{' '}
                                </li>
                                <li>
                                  <span className="m-0 flex items-center gap-1">
                                    20 sujets
                                    {pricing.title == 'DECOUVERTE' ||
                                    pricing.title == 'INTENSE' ? (
                                      <FaTimes className="text-red-500" />
                                    ) : (
                                      <ImCheckboxChecked className="text-emerald-400" />
                                    )}
                                  </span>{' '}
                                </li>
                              </ol>
                            </li>
                          );
                        }
                      })}
                    </ol>
                  </div>
                  <PathDsg />
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
      <TestModal ref={alert} />
    </motion.section>
  );
}

export default ChoosePack;

export const PathDsg = () => {
  return (
    <div>
      <span className="absolute right-0 top-7 z-[-1]">
        <svg
          width="77"
          height="172"
          viewBox="0 0 77 172"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle cx="86" cy="86" r="86" fill="url(#paint0_linear)" />
          <defs>
            <linearGradient
              id="paint0_linear"
              x1="86"
              y1="0"
              x2="86"
              y2="172"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#3056D3" stop-opacity="0.09" />
              <stop offset="1" stopColor="#C4C4C4" stop-opacity="0" />
            </linearGradient>
          </defs>
        </svg>
      </span>
      <span className="absolute right-4 top-4 z-[-1]">
        <svg
          width="41"
          height="89"
          viewBox="0 0 41 89"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            cx="38.9138"
            cy="87.4849"
            r="1.42021"
            transform="rotate(180 38.9138 87.4849)"
            fill="#3056D3"
          />
          <circle
            cx="38.9138"
            cy="74.9871"
            r="1.42021"
            transform="rotate(180 38.9138 74.9871)"
            fill="#3056D3"
          />
          <circle
            cx="38.9138"
            cy="62.4892"
            r="1.42021"
            transform="rotate(180 38.9138 62.4892)"
            fill="#3056D3"
          />
          <circle
            cx="38.9138"
            cy="38.3457"
            r="1.42021"
            transform="rotate(180 38.9138 38.3457)"
            fill="#3056D3"
          />
          <circle
            cx="38.9138"
            cy="13.634"
            r="1.42021"
            transform="rotate(180 38.9138 13.634)"
            fill="#3056D3"
          />
          <circle
            cx="38.9138"
            cy="50.2754"
            r="1.42021"
            transform="rotate(180 38.9138 50.2754)"
            fill="#3056D3"
          />
          <circle
            cx="38.9138"
            cy="26.1319"
            r="1.42021"
            transform="rotate(180 38.9138 26.1319)"
            fill="#3056D3"
          />
          <circle
            cx="38.9138"
            cy="1.42021"
            r="1.42021"
            transform="rotate(180 38.9138 1.42021)"
            fill="#3056D3"
          />
          <circle
            cx="26.4157"
            cy="87.4849"
            r="1.42021"
            transform="rotate(180 26.4157 87.4849)"
            fill="#3056D3"
          />
          <circle
            cx="26.4157"
            cy="74.9871"
            r="1.42021"
            transform="rotate(180 26.4157 74.9871)"
            fill="#3056D3"
          />
          <circle
            cx="26.4157"
            cy="62.4892"
            r="1.42021"
            transform="rotate(180 26.4157 62.4892)"
            fill="#3056D3"
          />
          <circle
            cx="26.4157"
            cy="38.3457"
            r="1.42021"
            transform="rotate(180 26.4157 38.3457)"
            fill="#3056D3"
          />
          <circle
            cx="26.4157"
            cy="13.634"
            r="1.42021"
            transform="rotate(180 26.4157 13.634)"
            fill="#3056D3"
          />
          <circle
            cx="26.4157"
            cy="50.2754"
            r="1.42021"
            transform="rotate(180 26.4157 50.2754)"
            fill="#3056D3"
          />
          <circle
            cx="26.4157"
            cy="26.1319"
            r="1.42021"
            transform="rotate(180 26.4157 26.1319)"
            fill="#3056D3"
          />
          <circle
            cx="26.4157"
            cy="1.4202"
            r="1.42021"
            transform="rotate(180 26.4157 1.4202)"
            fill="#3056D3"
          />
          <circle
            cx="13.9177"
            cy="87.4849"
            r="1.42021"
            transform="rotate(180 13.9177 87.4849)"
            fill="#3056D3"
          />
          <circle
            cx="13.9177"
            cy="74.9871"
            r="1.42021"
            transform="rotate(180 13.9177 74.9871)"
            fill="#3056D3"
          />
          <circle
            cx="13.9177"
            cy="62.4892"
            r="1.42021"
            transform="rotate(180 13.9177 62.4892)"
            fill="#3056D3"
          />
          <circle
            cx="13.9177"
            cy="38.3457"
            r="1.42021"
            transform="rotate(180 13.9177 38.3457)"
            fill="#3056D3"
          />
          <circle
            cx="13.9177"
            cy="13.634"
            r="1.42021"
            transform="rotate(180 13.9177 13.634)"
            fill="#3056D3"
          />
          <circle
            cx="13.9177"
            cy="50.2754"
            r="1.42021"
            transform="rotate(180 13.9177 50.2754)"
            fill="#3056D3"
          />
          <circle
            cx="13.9177"
            cy="26.1319"
            r="1.42021"
            transform="rotate(180 13.9177 26.1319)"
            fill="#3056D3"
          />
          <circle
            cx="13.9177"
            cy="1.42019"
            r="1.42021"
            transform="rotate(180 13.9177 1.42019)"
            fill="#3056D3"
          />
          <circle
            cx="1.41963"
            cy="87.4849"
            r="1.42021"
            transform="rotate(180 1.41963 87.4849)"
            fill="#3056D3"
          />
          <circle
            cx="1.41963"
            cy="74.9871"
            r="1.42021"
            transform="rotate(180 1.41963 74.9871)"
            fill="#3056D3"
          />
          <circle
            cx="1.41963"
            cy="62.4892"
            r="1.42021"
            transform="rotate(180 1.41963 62.4892)"
            fill="#3056D3"
          />
          <circle
            cx="1.41963"
            cy="38.3457"
            r="1.42021"
            transform="rotate(180 1.41963 38.3457)"
            fill="#3056D3"
          />
          <circle
            cx="1.41963"
            cy="13.634"
            r="1.42021"
            transform="rotate(180 1.41963 13.634)"
            fill="#3056D3"
          />
          <circle
            cx="1.41963"
            cy="50.2754"
            r="1.42021"
            transform="rotate(180 1.41963 50.2754)"
            fill="#3056D3"
          />
          <circle
            cx="1.41963"
            cy="26.1319"
            r="1.42021"
            transform="rotate(180 1.41963 26.1319)"
            fill="#3056D3"
          />
          <circle
            cx="1.41963"
            cy="1.4202"
            r="1.42021"
            transform="rotate(180 1.41963 1.4202)"
            fill="#3056D3"
          />
        </svg>
      </span>
    </div>
  );
};
