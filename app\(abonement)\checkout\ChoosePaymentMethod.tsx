import { motion } from 'framer-motion';
import Image from 'next/image';
import mtn from '@/public/momo.jpg';
import orange from '@/public/orange.jpg';
import paypal from '@/public/paypal.svg';
import card from '@/public/master-card.svg';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  parseAsNumberLiteral,
  parseAsStringLiteral,
  useQueryState,
} from 'nuqs';
import { METHODS } from '@/config';
interface ChoosePaymentMethodProps {
  steps: number[];
  prevStep: number;
}
function ChoosePaymentMethod({ steps, prevStep }: ChoosePaymentMethodProps) {
  const [, setStep] = useQueryState(
    'step',
    parseAsNumberLiteral(steps).withDefault(1),
  );

  const [, setMethod] = useQueryState(
    'method',
    parseAsStringLiteral(METHODS).withDefault('ORANGE'),
  );

  const { toast } = useToast();
  const { status } = useSession();
  const router = useRouter();
  const commingSoon = () => {
    toast({
      title: 'Moyen de paiement disponible bientôt',
    });
  };

  return (
    <motion.section
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{
        delay: 0,
        duration: 0.5,
        type: 'tween',
      }}
      className="prose relative z-20 flex min-h-full min-w-full flex-col items-center bg-white pb-3 pt-10 lg:pt-[60px]"
    >
      <Button
        variant={'outline'}
        size={'sm'}
        className="absolute left-3 top-3"
        onClick={() => {
          setStep(prevStep);
        }}
      >
        <ArrowLeft className="h-5 w-5 text-blue-500" />
      </Button>
      <h2>Moyen de paiement</h2>
      <p className="font-medium">
        Choisissez un moyen de paiement pour continuer
      </p>
      <div className="mx-auto flex items-center justify-center gap-3 font-semibold text-black">
        <button
          className="transition-all duration-300 hover:scale-105"
          onClick={() => {
            if (status == 'authenticated') {
              setMethod('MTN');
              setStep(prevStep + 2);
            } else if (status == 'unauthenticated') {
              router.push(`/auth?callbackUrl=/checkout?step=2`);
            } else {
            }
          }}
        >
          <div className="flex h-24 w-24 items-center justify-center overflow-hidden rounded-sm shadow-md shadow-slate-400/70">
            <Image src={mtn} alt="mtn" />
          </div>
          <span>MTN</span>
        </button>
        <button
          onClick={() => {
            if (status == 'authenticated') {
              setMethod('ORANGE');
              setStep(prevStep + 2);
            } else if (status == 'unauthenticated') {
              router.push(`/auth?callbackUrl=/checkout?step=2`);
            } else {
            }
          }}
          className="transition-all duration-300 hover:scale-105"
        >
          <div className="flex h-24 w-24 items-center justify-center overflow-hidden rounded-sm shadow-md shadow-slate-400/70">
            <Image src={orange} alt="orange" />
          </div>
          <span>ORANGE</span>
        </button>
        <button
          className="flex flex-col items-center justify-center no-underline transition-all duration-300 hover:scale-105"
          onClick={async () => {
            if (status == 'authenticated') {
              setMethod('CARD');
              setStep(prevStep + 2);
            } else if (status == 'unauthenticated') {
              router.push(`/auth?callbackUrl=/checkout?step=2`);
            } else {
            }
          }}
        >
          <div className="flex h-24 w-24 items-center justify-center overflow-hidden rounded-sm shadow-md shadow-slate-400/70">
            <Image src={card} alt="Visa" />
          </div>
          <span>VISA</span>
        </button>
        <button
          onClick={commingSoon}
          className="transition-all duration-300 hover:scale-105"
        >
          <div className="flex h-24 w-24 items-center justify-center overflow-hidden rounded-sm shadow-md shadow-slate-400/70">
            <Image src={paypal} alt="paypal" />
          </div>
          <span>PAYPAL</span>
        </button>
      </div>
    </motion.section>
  );
}

export default ChoosePaymentMethod;
