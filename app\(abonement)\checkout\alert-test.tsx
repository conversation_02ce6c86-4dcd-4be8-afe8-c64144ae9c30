import { Button, buttonVariants } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import Link from 'next/link';
import React from 'react';
type Action = 'credit' | 'debit' | 'bonnus';
interface UserModalProps {}

export const TestModal = React.forwardRef<
  React.ElementRef<typeof Button>,
  UserModalProps
>(({}, ref) => (
  <Dialog>
    <DialogTrigger asChild>
      <Button className="hidden" ref={ref} variant="outline"></Button>
    </DialogTrigger>
    <DialogContent className="sm:max-w-[425px]">
      <DialogHeader>
        <DialogTitle>{"Type de l'examen"}</DialogTitle>
        <DialogDescription>
          {"Choisissez l'examen que vous voulez pratiquer"}
        </DialogDescription>
      </DialogHeader>
      <div className="flex w-full items-center justify-center gap-2">
        <Link
          href={'/test?type=TCF'}
          className={cn(buttonVariants({ variant: 'default', size: 'lg' }))}
        >
          TCF
        </Link>
        <Link
          href={'/examen/tef'}
          className={cn(buttonVariants({ variant: 'default', size: 'lg' }))}
        >
          TEF
        </Link>
      </div>
    </DialogContent>
  </Dialog>
));
TestModal.displayName = 'TestModal';
