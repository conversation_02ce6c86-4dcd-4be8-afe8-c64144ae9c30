'use client';
import { AnimatePresence } from 'framer-motion';
import { useDocumentTitle } from '@mantine/hooks';
import ChoosePack from './ChoosePack';
import ChoosePaymentMethod from './ChoosePaymentMethod';
import AcceptPayment from './AcceptPayment';
import ChooseExam from './ChooseExam';
import { useQueryState, parseAsNumberLiteral } from 'nuqs';
import { CHECKOUTSTEPS } from '@/config';

function Page() {
  useDocumentTitle('OC | Checkout');

  const [step] = useQueryState(
    'step',
    parseAsNumberLiteral(CHECKOUTSTEPS) // pass a readonly list of allowed values
      .withDefault(1),
  );

  return (
    <AnimatePresence mode="wait">
      <main className="relative grid min-h-[75vh] place-content-center">
        {step == 1 ? <ChoosePack /> : null}
        {step == 2 ? <ChooseExam /> : null}
        {step == 3 ? (
          <ChoosePaymentMethod steps={CHECKOUTSTEPS} prevStep={2} />
        ) : null}
        {step == 4 ? <AcceptPayment /> : null}
      </main>
    </AnimatePresence>
  );
}

export default Page;
