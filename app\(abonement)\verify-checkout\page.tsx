'use client';

import { Button } from '@/components/ui/button';
import API from '@/lib/axios';
import { cn } from '@/lib/utils';
import { PDFDownloadLink } from '@react-pdf/renderer';
import { useQuery } from '@tanstack/react-query';
import { ArrowLeft, CheckCircle2, Loader2, X } from 'lucide-react';
import type { User } from 'next-auth';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useQueryState } from 'nuqs';
import { useEffect, useRef, useState } from 'react';
import { PDFDocument } from './pdf/receipt';

export interface ApiResponse {
  result: {
    status: string;
    app_transaction_ref: string;
    operator_transaction_ref: string;
    transaction_ref: string;
    transaction_type: string;
    transaction_amount: number;
    transaction_fees: number;
    transaction_currency: string;
    transaction_operator: string;
    transaction_status: string;
    transaction_reason: string;
    transaction_message: string | null;
    customer_phone_number: string;
  };
}

export default function Page() {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [istimeOut, setIsTimeOut] = useState<boolean>(false);
  const [ref] = useQueryState('ref');
  const [callbackUrl] = useQueryState('callbackUrl');
  const router = useRouter();
  const { update } = useSession();
  // Ajout d'une ref pour stocker le timeout
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const { data, isSuccess } = useQuery(
    ['check_paiement', ref],
    async () => {
      const { data } = await API.get<ApiResponse>(`api/coolPayments/${ref}`);
      return data;
    },
    {
      enabled: isLoading,
      refetchInterval: 1000,
      async onSuccess(data) {
        if (data.result.transaction_status.toLowerCase() === 'success') {
          // Clear le timeout si data reçue
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
          }
          const { data: user } = await API.get<User>(
            `/api/user/users/user-info`,
          );
          await update(user);
          setIsTimeOut(false);
          setIsLoading(false);
        }
      },
    },
  );

  useEffect(() => {
    // Stocke le timeout dans la ref
    timeoutRef.current = setTimeout(
      () => {
        setIsLoading(false);
        setIsTimeOut(true);
        setTimeout(() => router.push('/'), 1000 * 60 * 2);
      },
      1000 * 60 * 3,
    );
    // Nettoyage au démontage
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, []);

  // Helper pour afficher le statut

  return (
    <div className="container flex max-w-7xl flex-col gap-5 pt-20 text-center">
      <h1 className="text-4xl font-bold tracking-wide">
        Vérification de la transaction
      </h1>
      <h3 className="text-xl font-bold tracking-wide">
        Nous vérifions votre transaction
      </h3>
      <p className="text-base font-medium tracking-wide text-gray-500">
        Merci de patienter pendant la vérification de votre paiement.
      </p>

      <div
        className={cn(
          'mx-auto flex max-w-fit flex-col items-center justify-center rounded-sm border p-4',
          {
            'bg-zinc-100': !data && isLoading,
            'bg-emerald-500/80':
              data?.result.transaction_status.toLowerCase() === 'success',
            'bg-red-500':
              !isLoading &&
              (istimeOut ||
                (data &&
                  data.result.transaction_status.toLowerCase() !== 'success')),
          },
        )}
      >
        {!data && (
          <>
            <Loader2 className="h-10 w-10 animate-spin text-blue-500" />
            <p>Vérification en cours...</p>
          </>
        )}
        {istimeOut && (
          <>
            <X className="h-14 w-14 text-white" />
            <p className="max-w-[18ch] font-semibold text-white">
              Désolé, la vérification a pris trop de temps
            </p>
          </>
        )}
        {isSuccess && data && (
          <RenderStatus
            status={data?.result.transaction_status}
            OnFailed={() => {
              setIsTimeOut(false);
              setIsLoading(false);
              setTimeout(() => {
                router.replace('/');
              }, 1000 * 5);
            }}
          />
        )}
      </div>

      {/* Affichage des détails de la transaction si disponibles */}
      {isSuccess &&
        data &&
        data.result.transaction_status.toLowerCase() === 'success' && (
          <div className="mx-auto mb-4 mt-2 flex max-w-xl flex-col gap-2">
            <div className="flex items-center justify-end gap-4">
              {/* Remplace le bouton par PDFDownloadLink */}
              <PDFDownloadLink
                document={<PDFDocument data={data} />}
                fileName={`recu-transaction-${data.result.transaction_ref}.pdf`}
              >
                {({ loading }) => (
                  <Button disabled={loading}>
                    {loading ? 'Préparation...' : 'Télécharger le reçu'}
                  </Button>
                )}
              </PDFDownloadLink>
              <Button
                variant="secondary"
                onClick={() => router.push(callbackUrl || '/')}
              >
                <ArrowLeft className="mr-2 h-4 w-4" /> Retour
              </Button>
            </div>
            {/* ...existing code pour l'affichage HTML du reçu... */}
            <div className="w-full rounded-lg border border-gray-200 bg-white p-4 text-left shadow-lg sm:p-6">
              <h4 className="mb-4 flex items-center gap-2 text-sm font-semibold text-blue-700 sm:text-lg">
                <CheckCircle2 className="h-5 w-5 text-emerald-500" />
                Détails de la transaction
              </h4>
              <div className="flex flex-col gap-2 text-xs sm:text-base">
                <div className="flex flex-wrap items-center gap-x-2 gap-y-1">
                  <span className="font-semibold uppercase tracking-wide text-gray-600">
                    Montant :
                  </span>
                  <span className="flex items-center gap-1 break-all text-lg font-bold text-blue-800 sm:text-2xl">
                    {data.result.transaction_amount}{' '}
                    {data.result.transaction_currency}
                  </span>
                </div>
                <div className="flex flex-wrap items-center gap-x-2 gap-y-1">
                  <span className="font-semibold uppercase tracking-wide text-gray-600">
                    Opérateur :
                  </span>
                  <span className="break-all text-sm font-semibold text-gray-900 sm:text-lg">
                    {data.result.transaction_operator}
                  </span>
                </div>
                {data.result.app_transaction_ref && (
                  <div className="flex flex-wrap items-center gap-x-2 gap-y-1">
                    <span className="font-semibold uppercase tracking-wide text-gray-600">
                      Référence :
                    </span>
                    <span className="break-all font-mono text-xs text-gray-900 sm:text-base">
                      {data.result.app_transaction_ref}
                    </span>
                  </div>
                )}
                {data.result.customer_phone_number && (
                  <div className="flex flex-wrap items-center gap-x-2 gap-y-1">
                    <span className="font-semibold uppercase tracking-wide text-gray-600">
                      Téléphone :
                    </span>
                    <span className="break-all text-xs font-semibold text-gray-900 sm:text-base">
                      {data.result.customer_phone_number}
                    </span>
                  </div>
                )}
                {data.result.transaction_reason && (
                  <div className="flex flex-wrap items-center gap-x-2 gap-y-1">
                    <span className="font-semibold uppercase tracking-wide text-gray-600">
                      Description :
                    </span>
                    <span className="break-all text-xs text-gray-900 sm:text-base">
                      {data.result.transaction_reason}
                    </span>
                  </div>
                )}
                {data.result.transaction_message && (
                  <div className="flex flex-wrap items-center gap-x-2 gap-y-1">
                    <span className="font-semibold uppercase tracking-wide text-gray-600">
                      Message :
                    </span>
                    <span className="break-all text-xs text-gray-900 sm:text-base">
                      {data.result.transaction_message}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
    </div>
  );
}

function RenderStatus({
  status,
  OnFailed,
}: {
  status: string | undefined;
  OnFailed: () => void;
}) {
  useEffect(() => {
    if (
      status?.toLowerCase() === 'failed' ||
      status?.toLowerCase() === 'canceled'
    ) {
      OnFailed();
    }
  }, [status, OnFailed]);
  switch (status?.toLowerCase()) {
    case 'success':
      return (
        <>
          <CheckCircle2 className="h-14 w-14 text-white" />
          <p className="font-semibold text-white">Transaction réussie</p>
        </>
      );
    case 'failed':
    case 'canceled':
      return (
        <>
          <X className="h-14 w-14 text-white" />
          <p className="font-semibold text-white">
            Transaction échouée ou annulée
          </p>
        </>
      );
    default:
      return (
        <>
          <Loader2 className="h-10 w-10 animate-spin text-blue-500" />
          <p>Vérification en cours...</p>
        </>
      );
  }
}
