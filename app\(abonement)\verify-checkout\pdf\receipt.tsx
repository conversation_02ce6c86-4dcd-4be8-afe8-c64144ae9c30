import React from 'react';
import { View, Text, StyleSheet, Document, Page } from '@react-pdf/renderer';
import { ApiResponse } from '../page';

const styles = StyleSheet.create({
  container: {
    paddingTop: 35,
    paddingBottom: 65,
    paddingHorizontal: 35,
    fontSize: 11,
    fontFamily: 'Helvetica',
  },
  header: {
    textAlign: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    textTransform: 'uppercase',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 12,
    fontWeight: 'medium',
    color: '#444',
  },
  section: {
    marginBottom: 10,
    flexDirection: 'row',
  },
  label: {
    width: 100,
    fontWeight: 'bold',
    color: '#555',
  },
  value: {
    flex: 1,
    color: '#111',
    wordBreak: 'break-word',
    maxWidth: '100%',
  },
  descriptionValue: {
    fontSize: 10,
    color: '#111',
    textAlign: 'justify',
    lineHeight: 1.4,
  },
  divider: {
    marginVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#ccc',
    borderBottomStyle: 'solid',
  },
});

export const PDFDocument = ({ data }: { data: ApiResponse }) => {
  const ref = data.result.app_transaction_ref;
  const date = new Date(
    data.result.transaction_reason.split(' ')[2],
  ).toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'short',
    day: '2-digit',
  });

  return (
    <Document>
      <Page style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Objectif Canada</Text>
          <Text style={styles.subtitle}>Reçu de Paiement</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Date :</Text>
          <Text style={styles.value}>{date}</Text>
        </View>

        <View style={[styles.section]}>
          <Text style={styles.label}>Référence :</Text>
          <Text
            style={[
              styles.value,
              { textAlign: 'justify', fontSize: 8, fontWeight: '500' },
            ]}
          >
            {ref}
          </Text>
        </View>

        <View style={styles.divider} />

        <View style={styles.section}>
          <Text style={styles.label}>Montant :</Text>
          <Text style={styles.value}>
            {data.result.transaction_amount} {data.result.transaction_currency}
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Opérateur :</Text>
          <Text style={styles.value}>{data.result.transaction_operator}</Text>
        </View>

        {data.result.customer_phone_number && (
          <View style={styles.section}>
            <Text style={styles.label}>Téléphone :</Text>
            <Text style={styles.value}>
              {data.result.customer_phone_number}
            </Text>
          </View>
        )}

        {data.result.transaction_reason && (
          <View style={[styles.section]}>
            <Text style={styles.label}>Description :</Text>
            <Text style={[styles.descriptionValue, styles.value]}>
              {data.result.transaction_reason}
            </Text>
          </View>
        )}

        {data.result.transaction_message && (
          <View style={styles.section}>
            <Text style={styles.label}>Message :</Text>
            <Text style={styles.value}>{data.result.transaction_message}</Text>
          </View>
        )}
      </Page>
    </Document>
  );
};
