'use client';
import { buttonVariants } from '@/components/ui/button';
import Link from 'next/link';
import { useQueryState } from 'nuqs';

function AuthButtons() {
  const [callback] = useQueryState('callbackUrl');
  return (
    <>
      <Link
        href={`/signup?callbackUrl=${encodeURIComponent(callback ?? '/')}`}
        className={buttonVariants({ variant: 'default' })}
        replace={true}
      >
        Créer un compte
      </Link>
      <Link
        href={`/signin?callbackUrl=${encodeURIComponent(callback ?? '/')}`}
        className={buttonVariants({
          variant: 'ghost',
          className: 'border',
        })}
        replace={true}
      >
        Se connecter
      </Link>
    </>
  );
}

export default AuthButtons;
