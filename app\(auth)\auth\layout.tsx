import React from 'react';
import type { Metadata } from 'next';
import Image from 'next/image';
import Link from 'next/link';
import logo from '@/public/logo4.png';

export const metadata: Metadata = {
  title: 'TCF | Auth',
};
interface Props {
  children: React.ReactNode;
}
export default function layout({ children }: Props) {
  return (
    <div className="container mx-auto flex h-full max-w-7xl flex-col items-center justify-center md:pt-12">
      <Link
        href={'/'}
        className="mx-auto my-5 flex w-fit drop-shadow-md md:hidden"
      >
        <Image
          width={2476}
          height={2482}
          alt="logo"
          priority
          sizes="120px"
          className="h-[120px] w-[120px] rounded-full"
          src={logo}
        />
      </Link>
      {children}
    </div>
  );
}
