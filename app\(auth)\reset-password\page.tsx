'use client';
import ResetForm from '@/components/auth/ResetForm';
import Image from 'next/image';
import Link from 'next/link';
import logo from '@/public/logo4.png';
import authImg from '@/public/auth.webp';
export default function Page() {
  return (
    <div className="relative mb-20 block h-fit flex-col items-center justify-center overflow-hidden bg-white md:container md:grid md:h-[600px] md:rounded-md md:border md:px-4 md:py-10 md:shadow-md lg:max-w-none lg:grid-cols-2 lg:px-0 lg:py-0">
      {/* <div className='rounded-full drop-shadow-md bg-white overflow-hidden justify-center items-center p-4 absolute top-4 left-4  hidden md:flex' > */}
      <Link
        href={'/'}
        className="absolute left-4 top-4 my-5 hidden w-fit drop-shadow-md md:flex"
      >
        <Image
          width={2476}
          height={2482}
          alt="logo"
          priority
          sizes="120px"
          className="h-[120px] w-[120px] rounded-full"
          src={logo}
        />
      </Link>
      {/* </div> */}

      <div className="relative hidden h-full flex-col bg-muted p-10 text-white dark:border-r lg:flex">
        <div className="absolute inset-0">
          <Image
            fill
            priority
            alt="bg"
            src={authImg}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            className="object-cover"
          />
        </div>
        <div className="relative z-20 flex items-center text-lg font-medium">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2 h-6 w-6"
          >
            <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
          </svg>
          OBJECTIF CANADA
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            {/* <p className="text-2xl font-black drop-shadow-md">
                            {/* <Typical
                                steps={ASTUCES}
                                loop={Infinity}
                                wrapper="p"
                            /> 
                            {ASTUCES.at(current)}
                        </p> */}
          </blockquote>
        </div>
      </div>
      <div className="lg:m-0 lg:px-28 lg:py-8">
        <ResetForm />
      </div>
    </div>
  );
}
