import SignInForm from '@/components/auth/SignInForm';
import Image from 'next/image';
import authImg from '@/public/auth.webp';
import logo from '@/public/logo4.png';
import Link from 'next/link';
export default function Page({
  searchParams,
}: {
  params: { slug: string };
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  return (
    <div className="relative w-full lg:grid lg:min-h-[600px] lg:grid-cols-2 xl:min-h-[800px]">
      <div className="flex items-center justify-center py-12">
        <div className="w-[350px] space-y-4">
          <SignInForm callbackUrl={searchParams.callbackUrl as string} />
        </div>
      </div>
      <div className="hidden bg-muted lg:block">
        <Image
          src={authImg}
          alt="Image"
          width="1920"
          height="1080"
          className="h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
        />
      </div>
      <Link
        className="absolute left-4 top-4 hidden h-[100px] w-[100px] rounded-full md:block"
        href={'/'}
      >
        <Image src={logo} width={100} height={100} alt="Logo" />
      </Link>
    </div>
  );
}
