import { Serie } from '@/types';
import { notFound } from 'next/navigation';
import axios from 'axios';
import { Suspense } from 'react';
import { getAuthSession } from '@/lib/auth';
import { TCFCorrectionEE, TCFCorrectionEO } from './tcf-content';
import { TEFCorrectionEE, TEFCorrectionEO } from './tef-content';
import { getFreeSerrie } from '@/lib/free-serie-helper';
import Link from 'next/link';
import { buttonVariants } from '@/components/ui/button';
import logger from '@/lib/logger';

const getSerieTCF = async (libelle: string) => {
  const FREE_TCF_LIB = await getFreeSerrie('tcf');
  const session = await getAuthSession();
  const baseUrl =
    process.env.NEXT_PUBLIC_BASEURL_PROD ||
    process.env.NEXT_PUBLIC_BASEURL ||
    '';
  const path = FREE_TCF_LIB?.includes(libelle)
    ? `/offline/${libelle}`
    : `/${libelle}`;
  const { data } = await axios.get<Serie>(
    `${baseUrl}/api/serie/series${path}`,
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${session?.user.accessToken}`,
      },
    },
  );
  return data;
};

const getSerieTEF = async (libelle: string) => {
  const FREE_TEF_LIB = await getFreeSerrie('tef');
  const session = await getAuthSession();
  const baseUrl =
    process.env.NEXT_PUBLIC_BASEURL_PROD ||
    process.env.NEXT_PUBLIC_BASEURL ||
    '';
  const path = FREE_TEF_LIB?.includes(libelle)
    ? `/offline/${libelle}`
    : `/${libelle}`;
  const { data } = await axios.get<SerieTEF>(
    `${baseUrl}/api/tef/serie/series${path}`,
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${session?.user.accessToken}`,
      },
    },
  );
  return data;
};

async function CorrectionPage({
  params,
}: {
  params: { slug: string; mode: string; examen: string };
}) {
  const { slug, examen, mode } = params;
  if (Number.isNaN(Number(slug))) {
    logger.log('mauvais libelle');

    notFound();
  }
  let serie;
  try {
    if (examen === 'TCF') {
      serie = (await getSerieTCF(slug)) as Serie;
      if (!serie) {
        logger.log('pas de serie');
        notFound();
      }
    } else {
      serie = (await getSerieTEF(slug)) as SerieTEF;
      if (!serie) {
        logger.log('pas de serie');
        notFound();
      }
    }

    return (
      <Suspense>
        {examen === 'TCF' ? (
          <>
            {mode == 'EE' && <TCFCorrectionEE serie={serie as any} />}
            {mode == 'EO' && <TCFCorrectionEO serie={serie as any} />}
          </>
        ) : (
          <>
            {mode == 'EE' && <TEFCorrectionEE serie={serie as any} />}
            {mode == 'EO' && <TEFCorrectionEO serie={serie as any} />}
          </>
        )}
      </Suspense>
    );
  } catch (error) {
    // logger.log(error);

    return (
      <div className="md:p-x-0 grid !h-full place-content-center px-4">
        <div className="prose w-full max-w-3xl space-y-3 font-medium">
          <h2>Veuillez essayer plus tard</h2>
          <p className="text-center">Une erreur s&apos;est produite</p>
        </div>
        <Link
          className={buttonVariants({
            variant: 'outline',
            className: 'my-4 text-center',
          })}
          href={'/'}
        >
          Acceuil
        </Link>
      </div>
    );
  }
}

export default CorrectionPage;
