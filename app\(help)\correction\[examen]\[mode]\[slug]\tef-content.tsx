import { redirect } from 'next/navigation';
import parse from 'html-react-parser';
export function TEFCorrectionEE({ serie }: { serie: SerieTEF }) {
  if (serie.eeQuestions[0].sections.some((task) => !task.correction)) {
    redirect('/methodologie/TEF?discipline=PE');
  }

  return (
    <div className="md:p-x-0 grid h-full w-full place-content-center px-4">
      <div className="prose w-full max-w-3xl space-y-3">
        {serie.eeQuestions[0].sections.map((task) => (
          <div key={task._id} className="space-y-2">
            <h4 className="text-lg capitalize underline underline-offset-1">
              {task.libelle}
            </h4>
            <div className="space-y-1">
              <h6 className="text-center font-medium underline underline-offset-2">
                Consigne
              </h6>
              <div>{parse(task.consigne)}</div>
              <h6 className="text-center font-medium underline underline-offset-2">
                Correction
              </h6>
              <div>{parse(task.correction || '')}</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export function TEFCorrectionEO({ serie }: { serie: SerieTEF }) {
  if (serie.eoQuestions[0].sections.every((task) => !task.correction)) {
    return (
      <div className="md:p-x-0 grid h-full w-full place-content-center px-4">
        <div className="prose w-full max-w-3xl space-y-3 font-medium">
          Oups ! cette correction n&apos;est pas encore disponible
        </div>
      </div>
    );
  }

  return (
    <div className="md:p-x-0 grid h-full w-full place-content-center px-4">
      <div className="prose w-full max-w-3xl space-y-3">
        {serie.eoQuestions[0].sections.map(
          (task) =>
            task.correction && (
              <div key={task._id} className="space-y-2">
                <h4 className="text-lg capitalize underline underline-offset-1">
                  {task.libelle}
                </h4>
                <div className="space-y-1">
                  <h6 className="text-center font-medium underline underline-offset-2">
                    Consigne
                  </h6>
                  <div>{parse(task.consigne)}</div>
                  <h6 className="text-center font-medium underline underline-offset-2">
                    Correction
                  </h6>
                  <div>{parse(task.correction || '')}</div>
                </div>
              </div>
            ),
        )}
      </div>
    </div>
  );
}
