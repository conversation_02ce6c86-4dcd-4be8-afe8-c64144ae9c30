'use client';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useMediaQuery } from '@mantine/hooks';
import CETabsContent from './tabs/ce-tabs';
import COTabsContent from './tabs/co-tabs';
import EETabsContent from './tabs/ee-tabs';
import EOTabsContent from './tabs/eo-tabs';
import { useQueryState } from 'nuqs';

export function TCFTabs() {
  const isMobile = useMediaQuery('(max-width: 40em)', false, {
    getInitialValueInEffect: false,
  });

  const [discipline] = useQueryState('discipline');

  return (
    <Tabs
      defaultValue={discipline ?? 'CE'}
      className="mx-auto w-full max-w-screen-lg"
    >
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="CE">
          {isMobile ? 'CE' : 'COMPREHENSION ECRITE'}
        </TabsTrigger>
        <TabsTrigger value="CO">
          {isMobile ? 'CO' : 'COMPREHENSION ORALE'}
        </TabsTrigger>
        <TabsTrigger value="EE">
          {isMobile ? 'EE' : 'EXPRESSION ECRITE'}
        </TabsTrigger>
        <TabsTrigger value="EO">
          {' '}
          {isMobile ? 'EO' : 'EXPRESSION ORALE'}{' '}
        </TabsTrigger>
      </TabsList>
      <TabsContent value="CE" className="flex w-full justify-center">
        <CETabsContent />
      </TabsContent>
      <TabsContent value="CO" className="flex w-full justify-center">
        <COTabsContent />
      </TabsContent>
      <TabsContent value="EE" className="flex w-full justify-center">
        <EETabsContent />
      </TabsContent>
      <TabsContent value="EO" className="flex w-full justify-center">
        <EOTabsContent />
      </TabsContent>
    </Tabs>
  );
}
