'use client';
import { RxArrowUp } from 'react-icons/rx';
import { TEFTabs } from './TEFtabs';
import ScrollToTop from 'react-scroll-to-top';

function Page() {
  return (
    <div className="my-10 px-6 lg:px-20">
      <ScrollToTop
        className="flex items-center justify-center !shadow-md !shadow-white !drop-shadow-md"
        smooth
        component={<RxArrowUp className="h-5 w-5 font-bold text-blue-500" />}
      />
      <TEFTabs />
    </div>
  );
}

export default Page;
