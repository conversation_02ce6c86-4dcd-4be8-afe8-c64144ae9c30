'use client';
import { useTranslation } from '@/app/i18n/client';
import { usei18nState } from '@/context/i18n';
import { useDocumentTitle } from '@mantine/hooks';

import { useEffect } from 'react';

export default function Page() {
  const questionsAndAnswers = [
    {
      question: 'Comment obtenir C2 au TCF Canada?',
      answer:
        "Pour obtenir le Certificat de Compétences en Français de CIC - TCF Canada, vous devez vous inscrire à la version spécifique du TCF requise pour l'immigration, trouver un centre d'examen agréé, vous préparer si nécessaire, passer l'examen le jour convenu en suivant les consignes, puis attendre les résultats pour les utiliser dans votre demande d'immigration au Canada.",
    },
    {
      question: 'Comment payez un abonnement?',
      answer:
        "Dans le menu cliquez sur l'onglet abonnement, choissisez votre formule et puis confirmer votre achat sur votre téléphone",
    },
    {
      question: "Combien y a-t-il d'épreuves au TCF?",
      answer:
        '04 (quatre) épreuves à savoir : (1) compréhension orale, (2) compréhension écrite, (3) expression orale, (4) expression orale',
    },
    {
      question: "Comment s'inscrire au TCF ?",
      answer:
        "Rapprochez-vous de l'Institut français le plus proche pour vous inscrire",
    },
    {
      question: 'Puis-je devenir partenaire de Objectif Canada',
      answer:
        "Oui vous le pouvez. Contactez nous en précisant pour object 'demande de partenariat'.",
    },
  ];
  const { lng } = usei18nState();
  const { t } = useTranslation(lng);
  useDocumentTitle(`TCF | ${t('FAQ', { ns: 'navbar' })}`);
  useEffect(() => {
    let details: HTMLDetailsElement[] = [];
    document.querySelectorAll('details').forEach((det) => details.push(det));
    document.addEventListener('click', function (e) {
      if (!details.some((f) => f.contains(e.target as any))) {
        details.forEach((f) => f.removeAttribute('open'));
      } else {
        details.forEach((f) =>
          !f.contains(e.target as any) ? f.removeAttribute('open') : '',
        );
      }
    });
    return () => {
      document.removeEventListener('click', function (e) {
        if (!details.some((f) => f.contains(e.target as any))) {
          details.forEach((f) => f.removeAttribute('open'));
        } else {
          details.forEach((f) =>
            !f.contains(e.target as any) ? f.removeAttribute('open') : '',
          );
        }
      });
    };
  }, []);

  return (
    <div className="lg: mx-auto space-y-4 px-3 py-3 text-center lg:max-w-screen-xl lg:items-center lg:px-14">
      <h1 className="mb-10 text-2xl font-bold text-blue-500 md:text-3xl">
        Questions fréquemment posées
      </h1>
      {questionsAndAnswers.map((item, index) => (
        <details
          key={index}
          className="group rounded-md rounded-l-none border border-s-4 border-l-blue-500 p-6 [&_summary::-webkit-details-marker]:hidden"
        >
          <summary className="z-0 flex cursor-pointer items-center justify-between gap-1.5">
            <h2 className="max-w-none text-lg font-medium text-gray-900">
              {item.question}
            </h2>

            <span className="shrink-0 rounded-md p-1.5 text-gray-900 transition-all hover:bg-zinc-100 sm:p-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 shrink-0 transition duration-300 group-open:-rotate-45"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z"
                  clipRule="evenodd"
                />
              </svg>
            </span>
          </summary>

          <p className="prose prose-base mt-4 text-justify leading-relaxed text-gray-700 lg:prose-lg">
            {item.answer}
          </p>
        </details>
      ))}
    </div>
  );
}
