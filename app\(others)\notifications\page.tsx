'use client';

import { Ghost } from 'lucide-react';
import ReactT<PERSON><PERSON>go from 'react-time-ago';
import Skeleton from 'react-loading-skeleton';
import Link from 'next/link';
import { useUpdateIsView } from '@/hooks/use-updateView';
import { checkPlural } from '@/lib/helpersFunction';
import { useGetExpressionResult } from '@/lib/hooks';

export default function Notification() {
  const { data, isLoading, refetch } = useGetExpressionResult();
  const { mutate: updateIsView } = useUpdateIsView({
    onSuccess: async () => {
      await refetch();
    },
  });

  return (
    <div className="flex min-w-[100%] flex-col p-4 md:px-20 md:py-8">
      <h1 className="mb-5 text-3xl font-bold md:ml-[25rem]">Notifications</h1>

      {!isLoading && data !== undefined && data.length > 0 ? (
        <div className="mx-auto max-w-min space-y-3">
          <p className="mx-auto font-medium text-gray-600">
            Vous avez {data.length} nouveau{checkPlural(data.length, 'x')}{' '}
            résultat{checkPlural(data.length, 's')}.
          </p>
          {data.map((result) => (
            <Link
              onClick={() =>
                updateIsView({
                  result,
                  mode: result.type,
                  examen: result.examen == 'TEF' ? 'tef/' : '',
                })
              }
              href={`/detailC/${result.examen}/${result.type}/${result._id}`}
              key={result._id}
              className="mx-auto mb-1 flex w-full cursor-pointer flex-col space-y-1 rounded-md border bg-slate-100 p-2 text-sm font-semibold md:w-[30rem]"
            >
              <div className="flex gap-2">
                <p>série : {result.serie.libelle}</p>
                <p className="ml-auto text-sm text-gray-500">
                  <ReactTimeAgo
                    locale="fr-FR"
                    date={new Date(result.resultat[0].createdAt).getTime()}
                  />
                </p>
              </div>
              <p className="font-normal">{`résultat d'experssion ${
                result.type == 'EE' ? 'ecrite' : 'orale'
              }  disponible`}</p>
            </Link>
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center gap-2">
          <Ghost className="h-8 w-8 text-gray-400" />
          <span className="font-bold text-gray-500">Rien à signaler</span>
        </div>
      )}
      {isLoading && <Skeleton height={50} count={3} />}
    </div>
  );
}
