import DetailHeaderC from '@/components/DetailHeader';
import DisplayQuestion from '@/components/DisplayQuestion';
import 'react-sweet-progress/lib/style.css';
import { Resultat } from '@/types';
import { notFound } from 'next/navigation';

import { Metadata } from 'next';
import SERVER_API from '@/lib/axios.server';
import ComprehensionResultHeaderTEF from '@/components/test/TEF/comprehension-result-header';
import {
  DisplayQuestionTEFCE,
  DisplayQuestionTEFCO,
} from '@/components/test/TEF/display-question-tef';
type partialMode = 'CE' | 'CO';
type Examen = 'TEF' | 'TCF';
export const metadata: Metadata = {
  title: 'TCF | Detail',
  description:
    "Application web pour s'exercer au test de connaissance du français",
};

const fetchDetail = async (
  id: string,
  mode: partialMode,
  examen: Examen,
): Promise<Resultat | undefined | null> => {
  let urlpath = '';
  if (mode == 'CE') urlpath = 'TestCE/TestCEs';
  if (mode == 'CO') urlpath = 'TestCO/TestCOs';

  const { data } = await SERVER_API.get<Resultat>(
    `/api/${examen == 'TCF' ? '' : 'tef/'}${urlpath}/${id}?populateSerie=true`,
  );
  return data;
};

export default async function Page({
  params,
}: {
  params: {
    examen: Examen;
    slug: string;
    mode: partialMode;
  };
}) {
  const detailSet = await fetchDetail(params.slug, params.mode, params.examen);

  if (!detailSet) {
    notFound();
  }

  return (
    <div
      className="flex h-fit w-[95vw] flex-col gap-5 px-5 py-10 md:w-full lg:px-28"
      suppressHydrationWarning
    >
      {params.examen == 'TCF' ? (
        <>
          <DetailHeaderC detailSet={detailSet} />
          <DisplayQuestion detailset={detailSet} />
        </>
      ) : (
        <>
          <ComprehensionResultHeaderTEF
            detailSet={detailSet as any}
            type={params.mode}
          />
          {params.mode == 'CE' && (
            <DisplayQuestionTEFCE detailset={detailSet as any} />
          )}
          {params.mode == 'CO' && (
            <DisplayQuestionTEFCO detailset={detailSet as any} />
          )}
        </>
      )}
    </div>
  );
}
