'use client';

import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button, buttonVariants } from '@/components/ui/button';
import { PenSquare, Verified, Wallet, X } from 'lucide-react';
import { useRef, useState } from 'react';
import { FilleulsContent, SoldeContent, UserNameContent } from './Child';
import { Session } from 'next-auth';
import {} from '@/lib/axios';
import { useSession } from 'next-auth/react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import Image from 'next/image';
import sideImg from '@/public/side.jpeg';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
const Contents = {
  PSEUDO: UserNameContent,
  SOLDE: SoldeContent,
  FILS: FilleulsContent,
};

export default function UserInfo({ severSession }: { severSession: Session }) {
  const { data: session } = useSession();
  const [current, SetCurent] = useState<'SOLDE' | 'PSEUDO' | 'FILS'>('PSEUDO');
  const dialogRef = useRef<HTMLDivElement | null>(null);
  const close = useRef<HTMLButtonElement | null>(null);
  const Content = Contents[current];

  return (
    <Card className="">
      <CardHeader>
        <CardDescription>Connecte en tant que</CardDescription>
        <CardTitle className="flex items-center gap-3">
          {session?.user.email}{' '}
          {session?.user.accountIsCheck ? (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Verified className="h-6 w-6 text-blue-500" />
                </TooltipTrigger>
                <TooltipContent>
                  <p>Compte vérifié</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          ) : null}
        </CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col gap-5 md:flex-row">
        <div className="space-y-4">
          <div className="grid gap-2 font-semibold">
            <Label>Votre pseudo</Label>
            <Button
              className="max-w-sm"
              onClick={() => {
                SetCurent('PSEUDO');
                dialogRef.current?.click();
              }}
              variant={'secondary'}
            >
              <PenSquare className="mr-4 h-5 w-5" />
              {session?.user.codePromo
                ? session.user.codePromo
                : severSession.user.codePromo
                  ? severSession.user.codePromo
                  : 'Pas encore definie'}
            </Button>
          </div>
          <div className="grid gap-2 font-semibold">
            <Label>Solde</Label>
            <Button
              className="max-w-sm"
              onClick={() => {
                SetCurent('SOLDE');
                dialogRef.current?.click();
              }}
              variant={'secondary'}
            >
              <Wallet className="mr-4 h-5 w-5" />
              {session?.user.solde ? session.user.solde : 0} Xaf
            </Button>
          </div>
          <div className="grid gap-2 font-semibold md:max-w-xl">
            <FilleulsContent />
          </div>
        </div>
        <div className="relative h-[400px] w-full max-w-lg">
          <Image
            src={sideImg}
            alt="sideImg"
            className="rounded-lg object-contain"
            fill
          />
        </div>
      </CardContent>
      <AlertDialog onOpenChange={(open) => {}}>
        <AlertDialogTrigger asChild>
          <div className="hidden" ref={dialogRef}></div>
        </AlertDialogTrigger>
        <AlertDialogContent className="min-w-[400px] max-w-fit">
          <AlertDialogCancel
            ref={close}
            className={buttonVariants({
              size: 'icon',
              variant: 'secondary',
              className: 'absolute right-1 top-1 p-0 font-bold',
            })}
          >
            <X className="h-6 w-6" />
          </AlertDialogCancel>
          <Content close={close} />
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  );
}
