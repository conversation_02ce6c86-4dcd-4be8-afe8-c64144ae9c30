const RemainDay = ({
  abonnementRemainDay,
  hidden,
}: {
  abonnementRemainDay?: number;
  hidden?: boolean;
}) => {
  if (hidden) return null;
  return (
    <div className={'relative'}>
      <h1
        className={
          'mb-0.5 bg-gradient-to-r from-green-300 via-blue-500 to-purple-600 bg-clip-text text-5xl font-bold text-transparent'
        }
      >
        {abonnementRemainDay ?? 0}
      </h1>
      <p className={'text-sm'}> Avant la fin de votre abonnement.</p>
      <p className={'absolute bottom-6 left-16 text-sm font-bold'}>jour(s)</p>
    </div>
  );
};

export default RemainDay;
