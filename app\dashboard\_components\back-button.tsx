'use client';

import { Button, buttonVariants } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export const BackToPrfile = () => {
  const { data: session } = useSession();
  const router = useRouter();
  if (session?.user.role == 'superdealer') {
    return (
      <Button
        variant={'outline'}
        onClick={() => router.back()}
        className="flex w-fit items-center gap-2"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Retour aux profils{' '}
      </Button>
    );
  }
  return (
    <Link
      href="/dashboard/profiles"
      className={buttonVariants({
        variant: 'outline',
        className: 'flex w-fit items-center gap-2',
      })}
    >
      <ArrowLeft className="mr-2 h-4 w-4" /> Retour aux profils
    </Link>
  );
};
