'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useRouter } from 'next/navigation';

export const BecameDealerCard = () => {
  const router = useRouter();
  const handleBecomeDealer = () => {
    router.push('/checkout-dealer?callbackUrl=/dashboard/profiles');
  };
  const handleQuit = () => {
    router.push('/dashboard/tcfhistory');
  };
  return (
    <Card>
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold">Attention !</CardTitle>
        <CardDescription className="max-w-sm text-base text-gray-900">
          Pour accéder à cette option vous devez être partenaire Objectif
          Canada.
        </CardDescription>
      </CardHeader>
      <CardContent className="">
        <div className="flex w-full items-center justify-center gap-4">
          <Button
            onClick={handleBecomeDealer}
            className="mt-4 w-full bg-green-400 hover:!bg-green-500"
          >
            Devenir Partenaire
          </Button>
          <Button
            variant={'destructive'}
            onClick={handleQuit}
            className="mt-4 w-full"
          >
            Quitter
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
