import { buttonVariants } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
export default function HistoryLoader() {
  return (
    <section className="relative z-10 h-fit overflow-hidden pt-2">
      <div className="flex flex-col gap-6 px-4 md:container">
        <div
          className={
            'mx-auto mt-5 flex flex-col items-center justify-between md:w-[75%] md:flex-row'
          }
        >
          <div>
            <h1
              className={
                'mb-0.5 bg-gradient-to-r from-green-300 via-blue-500 to-purple-600 bg-clip-text text-5xl font-bold text-transparent'
              }
            >
              <Skeleton className="h-[50px] w-[80px]" />
            </h1>
            <p className={'text-sm capitalize'}>Score global</p>
          </div>
          <div className={'relative'}>
            <h1
              className={
                'mb-0.5 bg-gradient-to-r from-green-300 via-blue-500 to-purple-600 bg-clip-text text-5xl font-bold text-transparent'
              }
            >
              <Skeleton className="h-[50px] w-[60px]" />
            </h1>
            <p className={'text-sm'}>Avant la fin de votre abonnement</p>
            <p className={'absolute bottom-6 left-16 text-sm font-bold'}>
              jour(s)
            </p>
          </div>
        </div>
        <div className="mx-auto flex max-w-[370px] items-center justify-center overflow-hidden px-4 py-5 md:max-w-full">
          <div className="mx-auto h-fit w-full overflow-hidden md:px-20">
            <Skeleton className="h-[400px] w-[650px]" />
          </div>
        </div>
        <div className="mx-auto mb-3 flex w-full flex-row-reverse flex-wrap items-center justify-center gap-5">
          <div className={buttonVariants({ variant: 'default' })}>
            {'Nouveau Test'}
          </div>
        </div>
      </div>
    </section>
  );
}
