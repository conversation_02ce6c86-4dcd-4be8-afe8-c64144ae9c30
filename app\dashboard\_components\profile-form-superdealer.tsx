'use client';

import { DialogContainer } from '@/components/modat-container';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useForm } from 'react-hook-form';
import { Button, buttonVariants } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Profile } from '@/types';
import { Edit, Trash2 } from 'lucide-react';

import {
  Card,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { useSession } from 'next-auth/react';
import API from '@/lib/axios';
import { useRef, useState } from 'react';
import { toast } from '@/components/ui/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { DialogClose } from '@radix-ui/react-dialog';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { revalidateServerPath } from '@/actions';
import CustomButton from '@/components/CustomButton';
import logger from '@/lib/logger';

const CreateProfileSchema = z.object({
  nom_profil: z.string().trim().min(1).max(30),
  pin: z.string().trim().min(4),
  balance_ee: z.coerce.number().gte(2).lte(99999),
  balance_eo: z.coerce.number().gte(2).lte(99999),
});

const UpdateProfileSchema = z.object({
  nom_profil: z.string().trim().min(1).max(30),
  pin: z.string().trim().min(4),
  balance_ee: z.coerce.number().gte(0).lte(99999),
  balance_eo: z.coerce.number().gte(0).lte(99999),
});
function CreateProfileForm() {
  const { groupId } = useParams();
  const router = useRouter();
  const closeRef = useRef<HTMLButtonElement>(null);
  const { data: session } = useSession();
  const [isLoading, setIsLoading] = useState(false);
  const form = useForm<z.infer<typeof CreateProfileSchema>>({
    resolver: zodResolver(CreateProfileSchema),
    defaultValues: {
      balance_ee: 2,
      balance_eo: 2,
    },
  });

  async function onSubmit(values: z.infer<typeof CreateProfileSchema>) {
    setIsLoading(true);
    const payload = {
      nom_profil: values.nom_profil,
      email_profil: session?.user.email,
      pin: values.pin,
      remains: {
        remainTCF: {
          balance_ee: values.balance_ee,
          balance_eo: values.balance_eo,
        },
        remainTEF: {
          balance_ee: 0,
          balance_eo: 0,
        },
      },
    };
    try {
      const res = await API.post<{ profile: Profile }>(
        '/api/dealer/profiles',
        payload,
      );

      const result = await API.post(`/api/superdealer/groups/add-profile`, {
        profileId: res.data.profile._id,
        groupId: groupId as string,
      });

      logger.log('result', result);

      toast({
        title: 'Profil créé avec succès',
        description: `Le profil ${res.data.profile.nom_profil} a été créé avec succès`,
      });

      await revalidateServerPath(`/dashboard/superdealer-groups/${groupId}`);
      await revalidateServerPath(`/dashboard/superdealer-groups`);
      router.refresh();
      form.reset();
      closeRef.current?.click();
    } catch (error: any) {
      toast({
        title: 'Erreur lors de la création du profil',
        description:
          error?.response?.data?.message || 'Une erreur est survenue',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form
        noValidate
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-2 px-2"
      >
        <div className="grid grid-cols-1 gap-6 md:grid-cols-1">
          <FormField
            control={form.control}
            name="nom_profil"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nom</FormLabel>
                <FormControl>
                  <Input placeholder="Saisir le nom" {...field} />
                </FormControl>
                <FormDescription></FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-1">
          <FormField
            control={form.control}
            name="pin"
            render={({ field }) => (
              <FormItem>
                <FormLabel>PIN (4 chiffres)</FormLabel>
                <FormControl>
                  <Input placeholder="PIN du profil" {...field} />
                </FormControl>
                <FormDescription></FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-1">
          <FormField
            control={form.control}
            name="balance_ee"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Solde EE</FormLabel>
                <FormControl>
                  <Input placeholder="" type="number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-1">
          <FormField
            control={form.control}
            name="balance_eo"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Solde EO</FormLabel>
                <FormControl>
                  <Input placeholder="" type="number" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex items-center gap-4">
          <CustomButton disabled={isLoading}>Créer</CustomButton>
          <DialogClose
            ref={closeRef}
            className={buttonVariants({ variant: 'outline' })}
          >
            Annuler
          </DialogClose>
        </div>
      </form>
    </Form>
  );
}

interface UpdateProfileFormProps {
  profile: Profile;
}
function UpdateProfileForm({ profile }: UpdateProfileFormProps) {
  const [canSeeEE, setCanSeeEE] = useState(false);
  const [canSeeEO, setCanSeeEO] = useState(false);
  const { data: session, update } = useSession();
  const query = useQueryClient();
  const router = useRouter();
  const closeRef = useRef<HTMLButtonElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const form = useForm<z.infer<typeof UpdateProfileSchema>>({
    resolver: zodResolver(UpdateProfileSchema),
    defaultValues: {
      nom_profil: profile.nom_profil,
      pin: profile.pin,
      balance_ee: 0,
      balance_eo: 0,
    },
  });

  async function onSubmit(values: z.infer<typeof UpdateProfileSchema>) {
    setIsLoading(true);
    const payload = {
      nom_profil: values.nom_profil,
      pin: values.pin,
      remains: {
        remainTCF: {
          balance_ee: canSeeEE ? values.balance_ee : 0,
          balance_eo: canSeeEO ? values.balance_eo : 0,
        },
        remainTEF: {
          balance_ee: 0,
          balance_eo: 0,
        },
      },
    };

    try {
      await API.put(`/api/dealer/profiles/${profile._id}`, payload);
      update({
        remainsDeals: {
          remainTCF: {
            balance_ee:
              session?.user.remainsDeals?.remainTCF?.balance_ee! -
              values.balance_ee,
            balance_eo:
              session?.user.remainsDeals?.remainTCF?.balance_eo! -
              values.balance_eo,
            remain_day: session?.user.remainsDeals?.remainTCF?.remain_day,
          },
          remainTEF: session?.user.remainsDeals?.remainTEF,
        },
      });
      await revalidateServerPath('/dashboard/profiles');
      router.refresh();
      query.invalidateQueries(['profiles']);
      form.reset();
      closeRef.current?.click();
      toast({
        title: 'Profil créé avec succès',
        description: `Le profil a été modifié avec succès`,
      });
    } catch (error: any) {
      toast({
        title: 'Erreur lors de la modification  du profil',
        description:
          error?.response?.data?.message || 'Une erreur est survenue',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <Form {...form}>
      <form
        noValidate
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-2 px-2"
      >
        <div className="grid grid-cols-1 gap-6 md:grid-cols-1">
          <FormField
            control={form.control}
            name="nom_profil"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Nom</FormLabel>
                <FormControl>
                  <Input placeholder="Saisir le nom" {...field} />
                </FormControl>
                <FormDescription></FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-1">
          <FormField
            control={form.control}
            name="pin"
            render={({ field }) => (
              <FormItem>
                <FormLabel>PIN</FormLabel>
                <FormControl>
                  <Input placeholder="PIN du profil" {...field} />
                </FormControl>
                <FormDescription></FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-1">
          {canSeeEE ? (
            <FormField
              control={form.control}
              name="balance_ee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Solde EE</FormLabel>
                  <FormControl>
                    <Input placeholder="" type="number" {...field} />
                  </FormControl>
                  <FormDescription>
                    Le solde sera augmenté de la valeur ajouée (
                    {profile.remains?.remainTCF.balance_ee} + {field.value ?? 0}
                    )
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : (
            <Button variant={'outline'} onClick={() => setCanSeeEE(true)}>
              Augmenter le solde EE
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-1">
          {canSeeEO ? (
            <FormField
              control={form.control}
              name="balance_eo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Solde EO</FormLabel>
                  <FormControl>
                    <Input placeholder="" type="number" {...field} />
                  </FormControl>
                  <FormDescription>
                    Le solde sera augmenté de la valeur ajouée (
                    {profile.remains?.remainTCF.balance_eo} + {field.value ?? 0}
                    )
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          ) : (
            <Button variant={'outline'} onClick={() => setCanSeeEO(true)}>
              Augmenter le solde EO
            </Button>
          )}
        </div>

        <div className="flex items-center gap-4">
          <CustomButton disabled={isLoading}>Sauvegarder</CustomButton>
          <DialogClose
            ref={closeRef}
            className={buttonVariants({ variant: 'outline' })}
          >
            Annuler
          </DialogClose>
        </div>
      </form>
    </Form>
  );
}

function DeleteProfileForm({ profile }: { profile: Profile }) {
  const [isLoading, setIsLoading] = useState(false);
  const query = useQueryClient();
  const closeRef = useRef<HTMLButtonElement>(null);
  const deleteProfile = async () => {
    setIsLoading(true);
    try {
      await API.delete(`/api/dealer/profiles/${profile._id}`);
      toast({
        title: 'Profil supprimé avec succès',
        description: `Le profil ${profile.nom_profil} a été supprimé avec succès`,
      });
      await revalidateServerPath('/dashboard/profiles');
      query.invalidateQueries(['profiles']);
      closeRef.current?.click();
    } catch (error: any) {
      toast({
        title: 'Erreur lors de la suppression du profil',
        description:
          error?.response?.data?.message || 'Une erreur est survenue',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = () => {
    deleteProfile();
  };
  return (
    <Card className="border-none p-0">
      <CardHeader className="space-y-1 text-center">
        <CardTitle className="text-base">
          {'Etes-vous sûr de vouloir supprimer ce profil?'}
        </CardTitle>
        <CardDescription className="font-bold">
          Cette action est irréversible
        </CardDescription>
      </CardHeader>
      <CardFooter>
        <div className="flex w-full items-center justify-center gap-4">
          <CustomButton
            variant={'destructive'}
            onClick={handleDelete}
            disabled={isLoading}
            buttonClassName="mt-4 w-full bg-red-400 hover:!bg-red-500"
            overLayClassName="bg-red-400"
          >
            Supprimer le profil
          </CustomButton>
          <DialogClose
            disabled={isLoading}
            ref={closeRef}
            className={buttonVariants({
              variant: 'outline',
              className: 'mt-4 w-full',
            })}
          >
            Annuler
          </DialogClose>
        </div>
      </CardFooter>
    </Card>
  );
}

export const AddProfile = () => {
  return (
    <DialogContainer
      title="Ajouter un profil"
      description="Ajouter un profil à votre liste"
      content={<CreateProfileForm />}
    >
      <Button className="w-fit">Ajouter un profil</Button>
    </DialogContainer>
  );
};

export const UpdateProfile = ({ profile }: UpdateProfileFormProps) => {
  return (
    <DialogContainer
      title="Modifier le profil"
      description="Modifier le profil de votre candidat"
      content={<UpdateProfileForm profile={profile} />}
    >
      <Button size={'icon'} variant={'outline'}>
        <Edit className="h-4 w-4" />
      </Button>
    </DialogContainer>
  );
};

export const DeleteProfile = ({ profile }: { profile: Profile }) => {
  return (
    <DialogContainer
      title="Supprimer le profil"
      description="Supprimer le profil de votre candidat"
      srOnly={true}
      content={<DeleteProfileForm profile={profile} />}
    >
      <Button size={'icon'} variant={'destructive'}>
        <Trash2 className="h-4 w-4 text-white" />
      </Button>
    </DialogContainer>
  );
};
