'use client';
import { DataTable } from '@/components/table/data-table';
import { useGetProfilesQuery } from '@/hooks/useGetProfilesQuery';
import { profileColumns } from './profileColumn';
import { AlertDestructive } from '@/components/alert-message';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';

export const ProfilesList = ({ isEnable }: { isEnable: boolean }) => {
  const { data, isLoading, isError, refetch } = useGetProfilesQuery({
    isEnable,
  });

  return (
    <div className="max-w-[100dvw] overflow-hidden">
      {isError ? (
        <AlertDestructive description="Une erreur est survenue lors de la récupération des profils. Veuillez réessayer.">
          <Button
            className="bg-green-400 hover:!bg-green-500"
            onClick={() => refetch()}
          >
            Recharger
          </Button>
        </AlertDestructive>
      ) : null}
      {isLoading && isEnable ? (
        <Skeleton className="h-96 w-full rounded-md" />
      ) : (
        <DataTable columns={profileColumns} data={data || []} />
      )}
    </div>
  );
};
