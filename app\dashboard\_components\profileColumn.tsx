import { DataTableColumnHeader } from '@/components/table/data-table-column-header';
import { buttonVariants } from '@/components/ui/button';
import { Profile } from '@/types';
import { ColumnDef } from '@tanstack/react-table';
import { Eye } from 'lucide-react';
import { DeleteProfile, UpdateProfile } from './profile-form';
import CopyToClipboardButton from '@/components/copy';
import Link from 'next/link';

export const profileColumns: ColumnDef<Profile>[] = [
  {
    accessorKey: 'nom_profil',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nom" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue('nom_profil')}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'email_profil',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
    cell: ({ row }) => {
      const email = `${row.original.email_profil}/${row.original.nom_profil}`;
      return (
        <div className="flex items-center space-x-2">
          <span className="max-w-[500px] truncate font-medium">{email}</span>
          <CopyToClipboardButton text={email} />
        </div>
      );
    },
  },
  {
    accessorKey: 'remains',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Solde EE" />
    ),
    cell: ({ row }) => {
      const remains = row.original.remains?.remainTCF;
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {remains?.balance_ee.toString().padStart(2, '0')}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'remains',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Solde EO" />
    ),
    cell: ({ row }) => {
      const remains = row.original.remains?.remainTCF;
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {remains?.balance_eo.toString().padStart(2, '0')}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Date de création" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {new Date(row.getValue('createdAt')).toLocaleDateString('fr-FR', {
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
            })}
          </span>
        </div>
      );
    },
  },
  {
    id: 'actions',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Actions" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <Link
            className={buttonVariants({ size: 'icon' })}
            href={`/dashboard/profiles/${row.original.userId}`}
          >
            <Eye className="h-4 w-4" />
            <span className="sr-only">Voir le profil</span>
          </Link>
          <UpdateProfile profile={row.original} />
          <DeleteProfile profile={row.original} />
        </div>
      );
    },
  },
];
