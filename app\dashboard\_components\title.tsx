'use client';

import { useSearchParams } from 'next/navigation';

const getTitle = (examen: string, mode: string) => {
  switch (mode) {
    case 'EE':
      return 'Expression Écrite' + examen;
    case 'EO':
      return 'Expression Orale' + examen;
    default:
      return '';
  }
};

const EXAMENS = ['TCF', 'TEF'];

function CorrectionTitle() {
  const searchParams = useSearchParams();
  const examen = searchParams.get('examen') as string;
  const mode = searchParams.get('mode') as string;
  return (
    <h2 className="text-xl font-bold text-gray-800 lg:text-2xl">
      Sujets {getTitle(EXAMENS.includes(examen) ? examen : 'TCF', mode)}
    </h2>
  );
}

export default CorrectionTitle;
