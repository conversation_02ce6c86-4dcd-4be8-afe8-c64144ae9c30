import UserInfo from '../UserInfo';
import { getAuthSession } from '@/lib/auth';
import { redirect } from 'next/navigation';
import DashboardNavigation from '../_components/navigation';
import Partners from '@/components/partners';

export default async function Page() {
  const session = await getAuthSession();
  if (!session) redirect('/signin');
  return (
    <div className="w-full">
      <DashboardNavigation />
      <div className="mx-auto flex w-full flex-col gap-6 md:container">
        <UserInfo severSession={session} />
        <Partners />
      </div>
    </div>
  );
}
