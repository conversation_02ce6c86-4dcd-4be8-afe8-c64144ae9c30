import { buttonVariants } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import React, { Suspense } from 'react';
import CorrectionTitle from '../../_components/title';
import {
  CorrectionList,
  ExpressionLoader,
} from './_components/correction-list';

function page({
  searchParams,
}: {
  searchParams: { examen: string; mode: string };
}) {
  return (
    <div className="container relative mx-auto flex max-w-[100dvw] flex-col gap-6 overflow-hidden p-4">
      <Link
        href="/dashboard/administration"
        className={buttonVariants({
          variant: 'outline',
          className: 'flex w-fit items-center gap-2',
        })}
      >
        <ArrowLeft className="mr-2 h-4 w-4" /> Retour
      </Link>
      <CorrectionTitle />
      <Suspense fallback={<ExpressionLoader />}>
        <CorrectionList examen={searchParams.examen} mode={searchParams.mode} />
      </Suspense>
    </div>
  );
}

export default page;
