import { buttonVariants } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { Suspense } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import EETasks from '@/app/dashboard/profiles/correction/_components/ee-task-tef';
import Note from '@/app/dashboard/profiles/correction/_components/note-tef';
import BodyLoader from '@/app/dashboard/profiles/correction/_components/loader';
import { EOTasks } from '@/app/dashboard/profiles/correction/_components/eo-task-tef';

import { getAuthSession } from '@/lib/auth';
import logger from '@/lib/logger';
import { ResultatEE } from '@/types';
import { cache } from 'react';
import TaskSelector from '@/app/dashboard/profiles/correction/_components/task-selector-tef';

const baseUrl =
  process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || '';
const getWritingExpression = cache(async (slug: string) => {
  const session = await getAuthSession();
  const res = await fetch(`${baseUrl}/api/tef/eeTest/tests/${slug}`, {
    next: { revalidate: 60 },
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session?.user.accessToken}`,
    },
  });

  if (!res.ok) {
    logger.error('Error fectch', await res.json());
    throw new Error('Failed to fetch writing expression');
  }

  return (await res.json()) as ResultatEE<SerieTEF>;
});

const getOralExpression = cache(async (slug: string) => {
  const session = await getAuthSession();
  const res = await fetch(`${baseUrl}/api/tef/eoTest/tests/${slug}`, {
    next: { revalidate: 60 },
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session?.user.accessToken}`,
    },
  });

  if (!res.ok) {
    logger.error('Error fectch', await res.json());
    throw new Error('Failed to fetch writing expression');
  }

  return (await res.json()) as ResultatEE<SerieTEF>;
});

const getTitle = (testType: string) => {
  switch (testType) {
    case 'EE':
      return 'Production Écrite';
    case 'EO':
      return 'Production Orale';
    default:
      return '';
  }
};

function ProfileDashboard({
  params,
}: {
  params: { correctionId: string; testType: string };
}) {
  return (
    <main className="relative mx-auto flex max-w-[100dvw] flex-col gap-6 overflow-hidden p-1 pb-20 sm:container sm:px-8 sm:py-4 sm:pb-0">
      <div className="flex w-full items-center">
        <Link
          href={`/dashboard/administration/correction?examen=TEF&mode=${params.testType}`}
          className={buttonVariants({
            variant: 'outline',
            className: 'flex w-fit items-center sm:gap-2',
          })}
        >
          <ArrowLeft className="mr-1 h-4 w-4 sm:mr-2" /> Retour aux Sujets
        </Link>
        <h2 className="mx-auto text-lg font-bold text-blue-600 sm:text-2xl">
          {getTitle(params.testType)}
        </h2>
      </div>
      <div className="flex flex-col gap-4">
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0">
          <div className="fixed bottom-[env(safe-area-inset-bottom)] left-0 right-0 z-40 flex items-center justify-center bg-white/40 py-0.5 backdrop-blur-md sm:static sm:bg-transparent">
            <TaskSelector />
          </div>

          <Suspense fallback={<Skeleton className="h-8 w-80" />}>
            <Description id={params.correctionId} type={params.testType} />
          </Suspense>
          <div className="ml-auto flex items-end gap-2 text-xl font-bold sm:mx-0">
            <Note />
          </div>
        </div>
        <>
          <Suspense fallback={<BodyLoader />}>
            {params.testType == 'EE' ? (
              <WritingBody id={params.correctionId} />
            ) : (
              <OralBody id={params.correctionId} />
            )}
          </Suspense>
        </>
      </div>
    </main>
  );
}

export default ProfileDashboard;

const Description = async ({ id, type }: { id: string; type: string }) => {
  const data =
    type == 'EE' ? await getWritingExpression(id) : await getOralExpression(id);
  const name = data.user.email?.split('/')[1];

  return (
    <p className="md:text-sm lg:text-lg">
      Soumis par{' '}
      <span className="font-semibold">{name ?? data.user.email}</span> le{' '}
      {format(new Date(data.createdAt), 'dd MMMM yyyy à HH:mm')}{' '}
    </p>
  );
};

const WritingBody = async ({ id }: { id: string }) => {
  const data = await getWritingExpression(id);
  return (
    <div className="">
      <EETasks result={data} />
    </div>
  );
};

const OralBody = async ({ id }: { id: string }) => {
  const data = await getOralExpression(id);
  return (
    <div className="">
      <EOTasks result={data} />
    </div>
  );
};
