import { buttonVariants } from '@/components/ui/button';
import Link from 'next/link';
import React, { Suspense } from 'react';
import DashboardNavigation from '../_components/navigation';
import logger from '@/lib/logger';
import { ExpressionTest } from '@/types';
import axios from 'axios';
import { getAuthSession } from '@/lib/auth';

export const metadata = {
  title: 'OC | Administration',
};

const corrections = [
  {
    name: 'EE',
    title: 'Expression écrite',
  },
  {
    name: 'EO',
    title: 'Expression orale',
  },
] as const;

function page() {
  return (
    <div className="w-full">
      <DashboardNavigation />
      <div className="mx-auto grid max-w-md grid-cols-2 grid-rows-2 place-content-center gap-4">
        {corrections.map((correction) => (
          <Link
            key={correction.name}
            href={`/dashboard/administration/correction?examen=TCF&mode=${correction.name}`}
            className={buttonVariants({
              variant: 'outline',
              className: 'relative !py-10 text-sm font-bold lg:text-base',
            })}
          >
            <span>{correction.title} TCF</span>
            <Suspense fallback={<CountLoader />}>
              <Count mode={correction.name} examen="TCF" />
            </Suspense>
          </Link>
        ))}
        {corrections.map((correction) => (
          <Link
            key={correction.name}
            href={`/dashboard/administration/correction?examen=TEF&mode=${correction.name}`}
            className={buttonVariants({
              variant: 'outline',
              className: 'relative !py-10 text-sm font-bold lg:text-base',
            })}
          >
            {correction.title} TEF
            <Suspense fallback={<CountLoader />}>
              <Count mode={correction.name} examen="TEF" />
            </Suspense>
          </Link>
        ))}
      </div>
    </div>
  );
}

export default page;

const baseUrl =
  process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || '';
async function getTests(token: string, mode: string, examen: string) {
  try {
    const { data } = await axios.get<any>(
      `${baseUrl}/api/${examen == 'TCF' ? '' : 'tef/'}${mode == 'EE' ? 'eeTest/tests' : 'eoTest/tests'}${examen == 'TCF' ? '/user' : ''}/pending`,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      },
    );
    let result: any[] = [];
    if (examen == 'TEF') {
      result = data.map((test: ExpressionTest) => ({
        ...test,
        type: mode as any,
        examen: examen.toLowerCase() as any,
      }));
    } else {
      result = data.tests.map((test: ExpressionTest) => ({
        ...test,
        type: mode as any,
        examen: examen.toLowerCase() as any,
      }));
    }

    return { success: true, data: result };
  } catch (error) {
    logger.error('Erreur lors de la récupération des tests:', error);
    if (axios.isAxiosError(error)) {
      if (error.response?.status == 401) {
        return { success: false, error: 'Session expirée' };
      } else if (error.response?.status == 404) {
        return { success: true, data: [] };
      }
    } else if (error instanceof Error) {
      if (error.message === 'Network Error') {
        return { success: false, error: 'Verifier votre connexion internet' };
      }
    }
    return { success: false, error: 'Erreur inconnue' };
  }
}

const Count = async ({ mode, examen }: { mode: string; examen: string }) => {
  const session = await getAuthSession();
  const token = session?.user.accessToken || '';
  const data = await getTests(token, mode, examen);

  if (!data.success) {
    return (
      <div className="absolute -right-1 -top-1 flex h-7 w-7 items-center justify-center rounded-full bg-red-600 text-xs text-white">
        x
      </div>
    );
  }
  return (
    <div className="absolute -right-1 -top-1 flex h-7 w-7 items-center justify-center rounded-full bg-green-600 text-xs text-white">
      {data.data?.length}
    </div>
  );
};

const CountLoader = () => {
  return (
    <div className="absolute -right-1 -top-1 flex h-7 w-7 items-center justify-center rounded-full bg-gray-600 text-xs text-white">
      <span className="h-3 w-3 animate-pulse rounded-full bg-white"></span>
    </div>
  );
};
