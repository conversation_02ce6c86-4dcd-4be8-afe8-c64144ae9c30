'use client';
import React, { PropsWithChildren } from 'react';
import { StoragePayload, useCorrectionStore } from '@/context/correction';
import { useParams } from 'next/navigation';
import { useEffect } from 'react';
function Layout({ children }: PropsWithChildren) {
  const { correctionId } = useParams();

  // Sélectionner uniquement les propriétés nécessaires
  const currentTask = useCorrectionStore((state) => state.currentTask);
  const noteTaskOne = useCorrectionStore((state) => state.noteTaskOne);
  const noteTaskTwo = useCorrectionStore((state) => state.noteTaskTwo);
  const noteTaskThree = useCorrectionStore((state) => state.noteTaskThree);
  const commentTaskOne = useCorrectionStore((state) => state.commentTaskOne);
  const commentTaskTwo = useCorrectionStore((state) => state.commentTaskTwo);
  const commentTaskThree = useCorrectionStore(
    (state) => state.commentTaskThree,
  );

  const uploadPreviousData = useCorrectionStore(
    (state) => state.uploadPreviousData,
  );

  useEffect(() => {
    const isDirty =
      noteTaskOne !== undefined ||
      noteTaskTwo !== undefined ||
      noteTaskThree !== undefined ||
      commentTaskOne !== undefined ||
      commentTaskTwo !== undefined ||
      commentTaskThree !== undefined;

    if (isDirty) {
      const payload = {
        currentTask,
        noteTaskOne,
        noteTaskTwo,
        noteTaskThree,
        commentTaskOne,
        commentTaskTwo,
        commentTaskThree,
      };
      localStorage.setItem(
        `correction-${correctionId}`,
        JSON.stringify(payload),
      );
    }
  }, [
    correctionId,
    currentTask,
    noteTaskOne,
    noteTaskTwo,
    noteTaskThree,
    commentTaskOne,
    commentTaskTwo,
    commentTaskThree,
  ]);

  useEffect(() => {
    const stringP = localStorage.getItem(`correction-${correctionId}`);
    if (!stringP) return;

    const payload = JSON.parse(stringP) as StoragePayload;
    uploadPreviousData(payload);
  }, [correctionId, uploadPreviousData]);

  return <>{children}</>;
}

export default Layout;
