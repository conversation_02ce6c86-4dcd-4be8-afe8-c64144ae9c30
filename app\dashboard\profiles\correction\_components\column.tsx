import { DataTableColumnHeader } from '@/components/table/data-table-column-header';
import { ExpressionTestResult } from '@/types';
import { ColumnDef } from '@tanstack/react-table';
import Link from 'next/link';
import format from 'date-fns/format';
export const correctionColumns: ColumnDef<ExpressionTestResult>[] = [
  {
    accessorKey: 'serie',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Serie" />
    ),
    cell: ({ row }) => {
      return (
        <Link
          href={`/dashboard/profiles/correction/${row.original.type}/${row.original._id}`}
          title="Faire la correction"
        >
          <div className="flex space-x-2">
            <span className="max-w-[500px] truncate font-medium">
              Série {row.original.serie.libelle}
            </span>
          </div>
        </Link>
      );
    },
  },

  {
    accessorFn: (row) => row.user.email,
    id: 'email',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
    cell: ({ row }) => {
      return (
        <Link
          href={`/dashboard/profiles/correction/${row.original.type}/${row.original._id}`}
          title="Faire la correction"
        >
          <div className="flex space-x-2">
            <span className="max-w-[500px] truncate font-medium">
              {row.original.user.email}
            </span>
          </div>
        </Link>
      );
    },
  },
  {
    accessorKey: 'user',
    accessorFn: (row) => row.user.email.split('/')[1],
    id: 'name',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Nom" />
    ),
    cell: ({ row }) => {
      const name = row.original.user.email.split('/')[1];
      return (
        <Link
          href={`/dashboard/profiles/correction/${row.original.type}/${row.original._id}`}
          title="Faire la correction"
        >
          <div className="flex space-x-2">
            <span className="max-w-[500px] truncate font-medium">{name}</span>
          </div>
        </Link>
      );
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Fait le" />
    ),
    cell: ({ row }) => {
      return (
        <Link
          href={`/dashboard/profiles/correction/${row.original.type}/${row.original._id}`}
          title="Faire la correction"
        >
          <div className="flex space-x-2">
            <span className="max-w-[500px] truncate font-medium">
              {format(
                new Date(row.getValue('createdAt')),
                "dd/MM/yyyy' 'HH:mm:ss",
              )}
            </span>
          </div>
        </Link>
      );
    },
  },
  // {
  //   id: 'actions',
  //   header: ({ column }) => (
  //     <DataTableColumnHeader column={column} title="Actions" />
  //   ),
  //   cell: ({ row }) => {
  //     return (
  //       <div className="flex space-x-2">
  //         <Link
  //           href={`/dashboard/profiles/correction/${row.original.type}/${row.original._id}`}
  //           className={buttonVariants({ variant: 'outline', size: 'icon' })}
  //           title="Faire la correction"
  //         >
  //           <BookOpenCheck className="h-5 w-5 text-blue-600" />
  //           <span className="sr-only">Faire la correction</span>
  //         </Link>
  //       </div>
  //     );
  //   },
  // },
];
