'use client';

import { useCorrectionStore } from '@/context/correction';
import { cn, getExpressionNiveaau } from '@/lib/utils';

const Note = () => {
  const noteTaskOne = useCorrectionStore((state) => state.noteTaskOne) ?? 0;
  const noteTaskTwo = useCorrectionStore((state) => state.noteTaskTwo) ?? 0;
  const noteTaskThree = useCorrectionStore((state) => state.noteTaskThree) ?? 0;
  const note = noteTaskOne + noteTaskTwo + noteTaskThree;
  return (
    <>
      <p className="flex items-end gap-0.5">
        <span className="text-3xl">{note.toString().padStart(2, '0')}</span>
        <span>/20</span>
      </p>{' '}
      -{' '}
      <span
        className={cn('text-red-500', {
          'text-green-500': note >= 10,
        })}
      >
        {getExpressionNiveaau(note)}
      </span>
    </>
  );
};

export default Note;
