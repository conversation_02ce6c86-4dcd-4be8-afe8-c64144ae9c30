'use client';

import { buttonVariants } from '@/components/ui/button';
import API from '@/lib/axios';
import logger from '@/lib/logger';
import { ExpressionTest } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { Loader2, NotebookPen, Volume2 } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';

export const WritingNofification = () => {
  const { groupId } = useParams();
  const router = useRouter();
  const { data, isLoading } = useQuery({
    queryKey: ['correction-ee', { groupId }],
    queryFn: async () => {
      const res = await API.get<{ tests: ExpressionTest[] }>(
        `/api/eeTest/tests/pending/by-group/${groupId}`,
      );
      logger.log('EE', res.data);
      return res.data.tests || [];
    },
  });
  const handleClick = () => {
    router.push(`/dashboard/profiles/correction?groupId=${groupId}`);
  };
  return (
    <>
      <span
        onClick={handleClick}
        className={buttonVariants({
          variant: 'ghost',
          className:
            'flex !h-8 w-fit cursor-pointer gap-2 !rounded-full border !p-2',
        })}
      >
        {/* <NotebookPen /> */}
        <NotebookPen className="h-4 w-4 font-light text-gray-500" />
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : data ? (
          <span className="flex h-5 w-5 animate-buttonheartbeat items-center justify-center rounded-full bg-blue-500 p-2 text-[10px] text-white">
            {data.length < 100 ? data.length : '99+'}
          </span>
        ) : null}
      </span>
    </>
  );
};

export const OralNofification = () => {
  const { groupId } = useParams();
  const router = useRouter();
  const { data, isLoading } = useQuery({
    queryKey: ['correction-eo', { groupId }],
    queryFn: async () => {
      const res = await API.get<{ tests: ExpressionTest[] }>(
        `/api/eoTest/tests/pending/by-group/${groupId}`,
      );
      logger.log('EO', res.data);
      return res.data.tests || [];
    },
  });

  const handleClick = () => {
    router.push(`/dashboard/profiles/correction?groupId=${groupId}`);
  };

  return (
    <>
      <span
        onClick={handleClick}
        className={buttonVariants({
          variant: 'ghost',
          className:
            'flex !h-8 w-fit cursor-pointer gap-2 !rounded-full border !p-2',
        })}
      >
        <Volume2 className="h-4 w-4 font-light text-gray-500" />
        {isLoading ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : data ? (
          <span className="flex h-5 w-5 animate-buttonheartbeat items-center justify-center rounded-full bg-blue-500 p-2 text-[10px] text-white">
            {data.length < 100 ? data.length : '99+'}
          </span>
        ) : null}
      </span>
    </>
  );
};
