'use client';

import { buttonVariants } from '@/components/ui/button';
import API from '@/lib/axios';
import { ExpressionTest } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { NotebookPen, Volume2 } from 'lucide-react';
import { useRouter } from 'next/navigation';
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { forwardRef, useRef } from 'react';
import Link from 'next/link';

export const WritingNofification = ({ isEnable }: { isEnable: boolean }) => {
  const router = useRouter();
  const dialogButtonRef = useRef<HTMLButtonElement>(null);
  const { data, isLoading } = useQuery({
    queryKey: ['correction-ee'],
    queryFn: async () => {
      const res = await API.get<{ tests: ExpressionTest[] }>(
        '/api/dealer/profiles/testsee-en-cours',
      );
      return res.data.tests;
    },
    enabled: isEnable,
  });
  const handleClick = () => {
    if (isEnable) {
      router.push(`/dashboard/profiles/correction`);
    } else {
      dialogButtonRef.current?.click();
    }
  };
  return (
    <>
      <span
        onClick={handleClick}
        className={buttonVariants({
          variant: 'ghost',
          className:
            'flex !h-8 w-fit cursor-pointer gap-2 !rounded-full border !p-2',
        })}
      >
        {/* <NotebookPen /> */}
        <NotebookPen className="h-4 w-4 font-light text-gray-500" />
        {isLoading ? null : data?.length! > 0 ? (
          <span className="flex h-5 w-5 animate-buttonheartbeat items-center justify-center rounded-full bg-blue-500 p-2 text-xs text-white">
            {data?.length}
          </span>
        ) : null}
      </span>
      <BecomeDealerMessage ref={dialogButtonRef} />
    </>
  );
};

export const OralNofification = ({ isEnable }: { isEnable: boolean }) => {
  const router = useRouter();
  const dialogButtonRef = useRef<HTMLButtonElement>(null);
  const { data, isLoading } = useQuery({
    queryKey: ['correction-eo'],
    queryFn: async () => {
      const res = await API.get<{ tests: ExpressionTest[] }>(
        '/api/dealer/profiles/testseo-en-cours',
      );
      return res.data.tests;
    },
    enabled: isEnable,
  });

  const handleClick = () => {
    if (isEnable) {
      router.push(`/dashboard/profiles/correction`);
    } else {
      dialogButtonRef.current?.click();
    }
  };

  return (
    <>
      <span
        onClick={handleClick}
        className={buttonVariants({
          variant: 'ghost',
          className:
            'flex !h-8 w-fit cursor-pointer gap-2 !rounded-full border !p-2',
        })}
      >
        <Volume2 className="h-4 w-4 font-light text-gray-500" />
        {isLoading ? null : data?.length! > 0 ? (
          <span className="flex h-5 w-5 animate-buttonheartbeat items-center justify-center rounded-full bg-blue-500 p-2 text-xs text-white">
            {data?.length}
          </span>
        ) : null}
      </span>
      <BecomeDealerMessage ref={dialogButtonRef} />
    </>
  );
};
export const BecomeDealerMessage = forwardRef<HTMLButtonElement>((_, ref) => {
  return (
    <AlertDialog>
      <AlertDialogTrigger className="hidden" ref={ref}>
        open
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader className="mb-6">
          <AlertDialogTitle className="text-center text-xl">
            Attention !
          </AlertDialogTitle>
          <AlertDialogDescription className="mx-5 text-center text-base font-normal">
            Vous devez souscrire à un abonnement Professionnel pour ajouter un
            profil.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <div className="flex items-center gap-5">
          <Link
            href={'/checkout-dealer?callbackUrl=/dashboard/profiles'}
            className={buttonVariants({
              className: 'w-full bg-green-600 text-white hover:bg-green-400',
            })}
          >
            Souscrire Maintenant
          </Link>
          <AlertDialogCancel
            className={buttonVariants({
              variant: 'destructive',
              className: 'w-full',
            })}
          >
            Quitter
          </AlertDialogCancel>
        </div>
        <a
          className="text-center text-sm text-blue-500 underline underline-offset-2"
          href="https://youtube.com/source/PU4fq0VTux4/shorts?si=dvuKiAmjByMiR0JH"
          target="_blank"
        >
          C’est quoi un abonnement professionnel ?
        </a>
        <AlertDialogFooter></AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
});

BecomeDealerMessage.displayName = 'BecomeDealerMessage';
