'use client';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { StoragePayload, useCorrectionStore } from '@/context/correction';
import { useParams } from 'next/navigation';
import { useEffect } from 'react';

function TaskSelector() {
  const { correctionId } = useParams();
  const state = useCorrectionStore((state) => state);
  const uploadPreviousData = useCorrectionStore(
    (state) => state.uploadPreviousData,
  );

  useEffect(() => {
    const isDirty =
      state.noteTaskOne !== undefined ||
      state.noteTaskTwo !== undefined ||
      state.noteTaskThree !== undefined ||
      state.commentTaskOne !== undefined ||
      state.commentTaskTwo !== undefined ||
      state.commentTaskThree !== undefined;
    if (isDirty) {
      const payload = {
        currentTask: state.currentTask,
        noteTaskOne: state.noteTaskOne,
        noteTaskTwo: state.noteTaskTwo,
        noteTaskThree: state.noteTaskThree,
        commentTaskOne: state.commentTaskOne,
        commentTaskTwo: state.commentTaskTwo,
        commentTaskThree: state.commentTaskThree,
      };
      localStorage.setItem(
        `correction-${correctionId}`,
        JSON.stringify(payload),
      );
    }
  }, [correctionId, state]);

  useEffect(() => {
    const stringP = localStorage.getItem(`correction-${correctionId}`);
    if (!stringP) return;

    const payload = JSON.parse(stringP) as StoragePayload;
    uploadPreviousData(payload);
  }, [correctionId, uploadPreviousData]);

  const setCurrentTask = useCorrectionStore((state) => state.setCurrentTask);
  return (
    <div className="">
      <Tabs
        defaultValue="1"
        onValueChange={(value) => {
          setCurrentTask(parseInt(value));
        }}
        className="h-fit w-full"
      >
        <TabsList className="!relative !gap-3">
          <TabsTrigger
            value="1"
            className={`font-normal tracking-wider data-[state=active]:!bg-blue-500 data-[state=active]:!font-semibold data-[state=active]:!text-white`}
          >
            Tâche 1
          </TabsTrigger>
          <TabsTrigger
            value="2"
            className={`font-normal tracking-wider data-[state=active]:!bg-blue-500 data-[state=active]:!font-semibold data-[state=active]:!text-white`}
          >
            Tâche 2
          </TabsTrigger>

          <TabsTrigger
            value="3"
            className={`font-normal tracking-wider data-[state=active]:!bg-blue-500 data-[state=active]:!font-semibold data-[state=active]:!text-white`}
          >
            Tâche 3
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
}

export default TaskSelector;
