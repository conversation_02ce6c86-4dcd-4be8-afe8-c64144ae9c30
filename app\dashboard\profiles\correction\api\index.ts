import { getAuthSession } from '@/lib/auth';
import logger from '@/lib/logger';
import { ResultatEE } from '@/types';
import { cache } from 'react';

const baseUrl =
  process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || '';
export const getWritingExpression = cache(async (slug: string) => {
  const session = await getAuthSession();
  const res = await fetch(`${baseUrl}/api/eeTest/tests/pending/${slug}`, {
    next: { revalidate: 60 },
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session?.user.accessToken}`,
    },
  });

  if (!res.ok) {
    logger.error('Error fectch', await res.json());
    throw new Error('Failed to fetch writing expression');
  }

  return (await res.json()) as ResultatEE;
});

export const getOralExpression = cache(async (slug: string) => {
  const session = await getAuthSession();
  const res = await fetch(`${baseUrl}/api/eoTest/tests/pending/${slug}`, {
    next: { revalidate: 60 },
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${session?.user.accessToken}`,
    },
  });

  if (!res.ok) {
    logger.error('Error fectch', await res.json());
    throw new Error('Failed to fetch writing expression');
  }

  return (await res.json()) as ResultatEE;
});
