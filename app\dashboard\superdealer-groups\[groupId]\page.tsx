'use client';
import Partners from '@/components/partners';
import { DataTable } from '@/components/table/data-table';
import { Profile } from '@/types';
import {
  OralNofification,
  WritingNofification,
} from '../../profiles/correction/_components/notification-superdealer';
import { Group } from '@/types';
import { useParams } from 'next/navigation';
import API from '@/lib/axios';
import { useQuery } from '@tanstack/react-query';
import { Skeleton } from '@/components/ui/skeleton';
import Link from 'next/link';
import { buttonVariants } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { AddProfile } from '../../_components/profile-form-superdealer';
import { profileColumns } from '../../_components/profileColumn';

const getGroup = async (groupId: string) => {
  try {
    const { data } = await API.get<Group>(`/api/superdealer/groups/${groupId}`);
    return { group: data, error: false };
  } catch (error) {
    return { group: null, error: true };
  }
};

function ProfilesPage() {
  const { groupId } = useParams();
  const { data: group, isLoading } = useQuery({
    queryKey: ['group', groupId],
    queryFn: async () => {
      const res = await getGroup(groupId as string);
      return res.group;
    },
  });

  return (
    <main className="relative w-full max-w-[100dvw] overflow-hidden">
      <div className="flex flex-col gap-6">
        <Link
          href="/dashboard/superdealer-groups"
          className={buttonVariants({
            variant: 'outline',
            className: 'flex w-fit items-center gap-2',
          })}
        >
          <ArrowLeft className="mr-2 h-4 w-4" /> Retour aux groupes
        </Link>
        <div className="flex flex-col gap-6">
          <h2 className="inline-flex items-center text-xl font-semibold text-gray-800">
            <span className="mr-2">Profils des candidats du groupe</span>{' '}
            <>
              {isLoading ? (
                <Skeleton className="h-[28px] w-20" />
              ) : (
                <span className="font-bold text-gray-500">{group?.name}</span>
              )}
            </>
          </h2>
          <div className="flex items-center gap-4">
            <AddProfile />
            <OralNofification />
            <WritingNofification />
          </div>
          <ProfilesList profiles={group?.profiles || []} />
        </div>
        <Partners />
      </div>
    </main>
  );
}

export default ProfilesPage;

const ProfilesList = ({ profiles }: { profiles: Profile[] }) => {
  return (
    <div className="max-w-[100dvw] overflow-hidden">
      <DataTable columns={profileColumns} data={profiles} />
    </div>
  );
};
