'use client';
import { useRef } from 'react';
import { TESTNAME } from '@/constants/text';
import {
  AlertDialogFooter,
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import Link from 'next/link';
import { Button, buttonVariants } from '@/components/ui/button';
import { useTestState } from '@/context/test';
import { useQueryClient } from '@tanstack/react-query';
import { Resultat, ResultatEE, Row } from '@/types';

import { getNiveau, getNumber, getRowTEF, isProfile } from '@/lib/utils';

import { DataTable } from '@/components/table/data-table';
import { columns } from '@/components/table/colunm';

import ErrorComponent from '@/components/Error';

import { useTranslation } from '@/app/i18n/client';
import { usei18nState } from '@/context/i18n';
import { useRouter } from 'next/navigation';
import { useDocumentTitle } from '@mantine/hooks';
import { useUpdateIsView } from '@/hooks/use-updateView';
import { Loader } from 'lucide-react';
import { useSession } from 'next-auth/react';
import { getDayCount } from '@/lib/getDayCount';
import RemainDay from '../_components/Remainday';
import { Remain } from '@/types/next-auth';

interface NewResultat extends Resultat {
  type: string;
}
interface TEFContentDashboardProps {
  dataC?: NewResultat[];
  dataE?: ResultatEE[];
  dataO?: ResultatEE[];
  error?: boolean;
  remains?: Remain;
}
export default function TEFContentDashboard({
  dataC,
  dataE,
  dataO,
  error,
  remains,
}: TEFContentDashboardProps) {
  const router = useRouter();
  const ref = useRef<HTMLDivElement | null>(null);
  const { Restart, selectedRow, SetMode } = useTestState();
  const client = useQueryClient();
  const { data: session } = useSession();
  const days = 100;
  const { mutate: updateIsView } = useUpdateIsView({
    onSuccess: async () => {
      client.invalidateQueries(['notifs']);
    },
  });

  const rowData = dataC
    ?.filter((d) => d.serie)
    ?.map((res) => {
      return getRowTEF(res, res.type);
    })!;
  let newRow: Row[] = [];
  let rowRegroupted: any;
  if (rowData) {
    rowRegroupted = rowData.reduce((acc, obj) => {
      const serie = obj.serie;
      if (!acc[serie]) {
        acc[serie] = [];
      }
      acc[serie].push(obj);
      return acc;
    }, {} as any);

    newRow = Object.keys(rowRegroupted).map((key) => {
      const eeResult = dataE?.filter(
        (res) => res.serie.libelle === key && res.status == 'terminer',
      )[0];
      const eoResult = dataO?.filter(
        (res) => res.serie.libelle === key && res.status == 'terminer',
      )[0];

      const rows = rowRegroupted[key] as Row[];
      const r: Row = {
        time: '',
        serie: key,
        isEEview: true,
        isEOview: true,
        id: '',
        CE: '-',
        CO: '-',
        EE: '-',
        EO: '-',
        MOY: '-',
      };
      const CE = rows
        .filter((ts) => ts.CE !== '-')
        .sort((a, b) => {
          const d: any = new Date(a.time);
          const c: any = new Date(b.time);
          return c - d;
        })[0];
      const CO = rows
        .filter((ts) => ts.CO !== '-')
        .sort((a, b) => {
          const d: any = new Date(a.time);
          const c: any = new Date(b.time);
          return c - d;
        })[0];
      r.CE = CE ? CE.CE : '-';
      r.CO = CO ? CO.CO : '-';
      r.EE = eeResult
        ? getNiveau(eeResult.resultat.reduce((acc, obj) => acc + obj.note, 0))
        : '-';
      r.EO = eoResult
        ? getNiveau(eoResult.resultat.reduce((acc, obj) => acc + obj.note, 0))
        : '-';
      const moy = [
        CE ? CE.MOY : 'Z',
        CO ? CO.MOY : 'Z',
        eeResult
          ? getNiveau(eeResult.resultat.reduce((acc, obj) => acc + obj.note, 0))
          : 'Z',
        eoResult
          ? getNiveau(eoResult.resultat.reduce((acc, obj) => acc + obj.note, 0))
          : 'Z',
      ].sort();
      const times = [
        CO ? CO.time : 0,
        CE ? CE.time : 0,
        eeResult ? eeResult.createdAt : 0,
        eoResult ? eoResult.createdAt : 0,
      ];
      r.time = String(
        times.sort((a, b) => {
          const d: any = new Date(a);
          const c: any = new Date(b);
          return c - d;
        })[0],
      );
      r.MOY = moy[0];
      r.isEEview = eeResult?.isView!;
      r.isEOview = eoResult?.isView!;
      return r;
    });
  }
  dataE
    ?.filter((d) => d.status == 'terminer')
    .map((res) => {
      const exist = newRow.find((row) => row.serie == res.serie.libelle);
      if (!exist) {
        const note = getNiveau(
          res.resultat.reduce((acc, obj) => acc + obj.note, 0),
        );

        newRow.push({
          CE: '-',
          CO: '-',
          EE: note,
          EO: '-',
          id: '',
          serie: res.serie.libelle,
          time: res.createdAt,
          isEOview: true,
          isEEview: res.isView,
          MOY: note,
        });
      }
    });
  dataO
    ?.filter((d) => d.status == 'terminer')
    .map((res) => {
      const exist = newRow.find((row) => row.serie == res.serie.libelle);
      if (!exist) {
        const note = getNiveau(
          res.resultat.reduce((acc, obj) => acc + obj.note, 0),
        );

        newRow.push({
          CE: '-',
          CO: '-',
          EE: '-',
          EO: note,
          id: '',
          serie: res.serie.libelle,
          time: res.createdAt,
          isEOview: res.isView,
          isEEview: true,
          MOY: note,
        });
      } else if (exist && exist.EO == '-') {
        const note = getNiveau(
          res.resultat.reduce((acc, obj) => acc + obj.note, 0),
        );

        exist.EO = note;
        exist.isEOview = res.isView;
        exist.MOY = [exist.MOY, note].sort()[0];
        exist.time = String(
          [exist.time, res.createdAt].sort((a, b) => {
            const d: any = new Date(a);
            const c: any = new Date(b);
            return c - d;
          })[0],
        );
        const index = newRow.indexOf(exist);
        newRow.splice(index, 1);
        newRow.push(exist);
      }
    });
  const allMoy = newRow.flatMap((row) => row.MOY).sort();
  // setMoyenne()
  const { lng } = usei18nState();
  const { t } = useTranslation(lng);
  useDocumentTitle(`TCF | ${t('Mon Compte', { ns: 'navbar' })}`);

  const abonnementRemainDay = getDayCount(remains?.remain_day || null);
  return (
    <section className="relative z-10 h-fit overflow-hidden pt-2">
      <div className="flex flex-col gap-6 px-4 md:container">
        <div
          className={
            'mx-auto mt-5 flex flex-col items-center justify-between md:w-[75%] md:flex-row'
          }
        >
          <div>
            {allMoy[0] ? (
              <h1
                className={
                  'mb-0.5 bg-gradient-to-r from-green-300 via-blue-500 to-purple-600 bg-clip-text text-5xl font-bold text-transparent'
                }
              >
                {allMoy[0]}
              </h1>
            ) : !error && newRow.length > 0 ? (
              <Loader className="h-5 w-5 animate-spin text-blue-500" />
            ) : (
              <h1
                className={
                  'mb-0.5 bg-gradient-to-r from-green-300 via-blue-500 to-purple-600 bg-clip-text text-5xl font-bold text-transparent'
                }
              >
                A1
              </h1>
            )}
            <p className={'text-sm capitalize'}>score global</p>
          </div>
          {remains ? (
            <div className="mx-auto flex items-center justify-center gap-3 rounded-sm border-2 px-5 py-3 font-bold">
              <span>EE : {getNumber(remains?.balance_ee)}.</span>
              <span>EO : {getNumber(remains?.balance_eo)}.</span>
            </div>
          ) : null}
          <RemainDay
            abonnementRemainDay={abonnementRemainDay}
            hidden={isProfile(session?.user.email)}
          />
        </div>
        <div className="mx-auto flex max-w-[370px] items-center justify-center overflow-x-scroll px-4 py-5 md:max-w-full md:overflow-x-hidden">
          {newRow.length > 0 ? (
            <div className="mx-auto h-fit w-full overflow-x-scroll md:overflow-x-hidden md:px-20">
              <DataTable
                data={newRow}
                columns={columns as any}
                dialogRef={ref}
                sortOptions={[
                  {
                    id: 'time',
                    desc: true,
                  },
                ]}
              />
            </div>
          ) : error ? (
            <ErrorComponent
              sub="Impossible de récupérer vos informations"
              title="Oups"
              description="Verifiez votre connexion internet et reconnectez-vous!"
            />
          ) : (
            <ErrorComponent
              sub="Vous êtes nouveau!"
              title="Pas de panique"
              description="Faites un test et revenez voir vos résultats. La série 1001 est gratuite."
            />
          )}
        </div>
        <div className="mx-auto mb-3 flex w-full flex-row-reverse flex-wrap items-center justify-center gap-5">
          <Link
            onClick={() => Restart()}
            className={buttonVariants({ variant: 'default' })}
            href={'/examen/tef'}
          >
            {'Nouveau Test'}
          </Link>
        </div>
      </div>
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <div className="hidden" ref={ref}></div>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-center uppercase">
              Choisissez une épreuve.
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="flex w-full flex-wrap items-center justify-evenly text-center font-semibold">
                {TESTNAME.map((test) => (
                  <Button
                    onClick={() => {
                      SetMode(test.name);
                      const d = newRow.filter(
                        (r) => r.serie == selectedRow?.serie!,
                      ) as Row[];
                      const eeResult = dataE
                        ?.filter(
                          (res) =>
                            res.serie.libelle === selectedRow?.serie &&
                            res.status == 'terminer',
                        )
                        .sort((a, b) => {
                          const d: any = new Date(a.createdAt);
                          const c: any = new Date(b.createdAt);
                          return c - d;
                        })[0];
                      const eoResult = dataO
                        ?.filter(
                          (res) =>
                            res.serie.libelle === selectedRow?.serie &&
                            res.status == 'terminer',
                        )
                        .sort((a, b) => {
                          const d: any = new Date(a.createdAt);
                          const c: any = new Date(b.createdAt);
                          return c - d;
                        })[0];
                      if (test.name == 'CE') {
                        if (days > 0) {
                          router.push(
                            `/detail/TEF/${test.name}/${
                              dataC?.find(
                                (d) =>
                                  d?.serie?.libelle == selectedRow?.serie &&
                                  d.type == 'CE',
                              )?._id
                            }`,
                          );
                        } else {
                          router.push(
                            `/checkout?next=/detail/TEF/${test.name}/${
                              dataC?.find(
                                (d) =>
                                  d?.serie?.libelle == selectedRow?.serie &&
                                  d.type == 'CE',
                              )?._id
                            }&callbackUrl=/detail/TEF/${test.name}/${
                              dataC?.find(
                                (d) =>
                                  d?.serie?.libelle == selectedRow?.serie &&
                                  d.type == 'CE',
                              )?._id
                            }`,
                          );
                        }
                      }

                      if (test.name == 'CO') {
                        if (days > 0) {
                          router.push(
                            `/detail/TEF/${test.name}/${
                              dataC?.find(
                                (d) =>
                                  d?.serie?.libelle == selectedRow?.serie &&
                                  d.type == 'CO',
                              )?._id
                            }`,
                          );
                        } else {
                          router.push(
                            `/checkout?next=/detail/TEF/${test.name}/${
                              dataC?.find(
                                (d) =>
                                  d?.serie?.libelle == selectedRow?.serie &&
                                  d.type == 'CO',
                              )?._id
                            }&callbackUrl=/detail/TEF/${test.name}/${
                              dataC?.find(
                                (d) =>
                                  d?.serie?.libelle == selectedRow?.serie &&
                                  d.type == 'CO',
                              )?._id
                            }`,
                          );
                        }
                      }

                      if (test.name == 'EE') {
                        if (eeResult?.isView == false)
                          updateIsView({
                            result: eeResult!,
                            mode: 'EE',
                            examen: 'tef/',
                          });
                        if (days > 0) {
                          router.push(`/detailC/TEF/EE/${eeResult?._id}`);
                        } else {
                          router.push(
                            `/checkout?next=/detailC/TEF/EE/${eeResult?._id}&callbackUrl=/detailC/TEF/EE/${eeResult?._id}`,
                          );
                        }
                      }
                      if (test.name == 'EO') {
                        if (eoResult?.isView == false)
                          updateIsView({
                            result: eoResult!,
                            mode: 'EO',
                            examen: 'tef/',
                          });
                        if (days > 0) {
                          router.push(`/detailC/TEF/EO/${eoResult?._id}`);
                        } else {
                          router.push(
                            `/checkout?next=/detailC/TEF/EO/${eoResult?._id}&callbackUrl=/detailC/TEF/EO/${eoResult?._id}`,
                          );
                        }
                      }
                    }}
                    key={test.name}
                    disabled={selectedRow?.[test.name] === '-'}
                  >
                    {test.name}
                  </Button>
                ))}
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Annuler</AlertDialogCancel>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </section>
  );
}
