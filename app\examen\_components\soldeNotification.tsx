'use client';
import { Button } from '@/components/ui/button';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

export default function SoldeNotification({
  discipline,
  id,
  isRemain,
  type,
}: {
  discipline: string;
  id: string | number;
  isRemain: boolean;
  type: 'EE' | 'EO';
}) {
  const router = useRouter();
  return (
    <div className="flex max-w-md flex-col items-center gap-2 rounded-sm border bg-white p-3 shadow-md shadow-blue-300">
      <span className="p-0 text-center text-xl font-semibold text-orange-400">
        Aie... Correction impossible !
      </span>
      <span className="w-full p-0 text-center text-base font-bold">
        {isRemain
          ? "Votre abonnement a expiré, vous n'avez plus droit aux sujets d'expression"
          : `Vous avez épuisé le nombre de corrections en ${discipline} auxquelles
        vous aviez droit.`}
      </span>
      <span className="max-w-[40ch] p-0 text-center text-base font-bold">
        {isRemain
          ? 'Veuillez souscrire à un abonnement'
          : 'Veuillez recharger votre solde.'}
      </span>
      <small className="font-semibold text-rose-400">
        (Votre {type == 'EE' ? 'rédaction' : 'enregistrement'} sera supprimée
        dans 48h )
      </small>

      <div className="mt-5 flex w-full items-center justify-between gap-3">
        <Button
          onClick={() => {
            if (isRemain) {
              router.push('/checkout');
            } else {
              router.push('/renew-expression-credits/client');
            }
            toast.dismiss(id);
          }}
          className="animate-buttonheartbeat bg-green-500 uppercase text-white hover:bg-green-300"
        >
          {isRemain ? 'Payez un abonnement' : 'Rechargez maintenant'}
        </Button>
        <Button
          variant={'destructive'}
          className="uppercase"
          onClick={() => {
            toast.dismiss(id);
            router.back();
          }}
        >
          Plus tard
        </Button>
      </div>
    </div>
  );
}
