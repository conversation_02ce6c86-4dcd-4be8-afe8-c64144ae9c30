'use client';

import ErrorComponent from '@/components/Error';
import { Button } from '@/components/ui/button';
import { useFreeModeState } from '@/context/free';
import { useTestState } from '@/context/test';
import { Frown } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { startTransition, useCallback } from 'react';

export default function ErrorPage({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const router = useRouter();
  const { Restart: TestRestart } = useTestState();
  const { Restart: FreeRestart } = useFreeModeState();
  const GoHome = useCallback(() => {
    router.push('/'), FreeRestart();
    TestRestart();
  }, []);

  function handleReset() {
    startTransition(() => {
      reset();
      router.refresh();
    });
  }

  return (
    <div className="flex min-h-[calc(100vh-164px)] w-full flex-col items-center justify-center gap-5 px-5 lg:px-52">
      <Frown className="h-16 w-16 text-gray-500" />
      <ErrorComponent
        sub={`${
          error.message.split(' ').length < 6 ? error.message : 'Erreur inconnu'
        }`}
        title="Oups"
        description="Veuillez contacter nos services si le problème persiste."
      />

      <div className="space-x-4">
        <Button onClick={handleReset}>Essayer de nouveau</Button>
        <Button onClick={GoHome} variant={'secondary'}>
          {"Retouner a l'Acceuil"}
        </Button>
      </div>
    </div>
  );
}
