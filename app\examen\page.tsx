import { buttonVariants } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import Link from 'next/link';
import React from 'react';

function Page() {
  return (
    <div className="flex h-full items-center justify-center">
      <Card>
        <CardHeader>
          <CardTitle>Examen</CardTitle>
          <CardDescription>
            Choisissez le test que vous souhaitez faire s&apos;il vous plait
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center gap-1.5">
            <Link className={buttonVariants()} href={'/examen/tcf'}>
              TCF
            </Link>
            <Link className={buttonVariants()} href={'/examen/tef'}>
              TEF
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default Page;
