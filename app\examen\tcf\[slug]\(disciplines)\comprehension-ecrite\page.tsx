import { fechSerie } from '@/app/examen/actions';
import { getAuthSession } from '@/lib/auth';
import { isTestAvailable } from '@/lib/utils';
import { notFound } from 'next/navigation';
import React from 'react';
import TestCETCF from '../../../_components/testCE';
interface Props {
  params: {
    slug: string;
  };
}
async function Page({ params: { slug } }: Props) {
  const session = await getAuthSession();
  const serie = await fechSerie(slug, session?.user.accessToken || '', 'COM');

  if (!serie) return notFound();

  if (!isTestAvailable('CE', serie)) {
    return (
      <section>
        <h1>Test not available</h1>
      </section>
    );
  }

  return (
    <main className="w-full">
      <TestCETCF serie={serie} />
    </main>
  );
}

export default Page;
