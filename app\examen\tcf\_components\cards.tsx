'use client';
import { Card, CardContent } from '@/components/ui/card';
import { Serie } from '@/types';
import { isTestAvailable } from '@/lib/utils';
import { Clock, HelpCircle } from 'lucide-react';
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';

import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { DialogClose } from '@radix-ui/react-dialog';
type CardProps = {
  title: string;
  duration: string;
  questions: number;
  href: string;
  icon: React.ReactNode;
  serie: Serie;
  type: 'CE' | 'EE' | 'EO' | 'CO';
};
export const DisciplineCard = ({
  type,
  serie,
  title,
  duration,
  questions,
  href,
  icon,
}: CardProps) => {
  const isAvailable = isTestAvailable(type, serie);
  return (
    <div className="cursor-pointer">
      {isAvailable ? (
        <Dialog>
          <DialogTrigger asChild>
            <Card className="overflow-hidden border border-blue-200">
              <CardContent className="bg-zinc-50 text-slate-700">
                <div className="flex h-[150px] w-[200px] flex-col items-center justify-center">
                  <span className="h-20 w-20 text-blue-500">{icon}</span>
                  <p className="font-medium">{title}</p>
                </div>
                <div className="flex items-center gap-1.5">
                  <Clock className="h-4 w-4" />
                  <p>{duration} minutes</p>
                </div>
                <div className="flex items-center gap-1.5">
                  <HelpCircle className="h-4 w-4" />
                  <p>
                    {questions}{' '}
                    {type == 'EE' || type == 'EO' ? 'tâches' : 'questions'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </DialogTrigger>
          <DialogContent>
            <DialogTitle>Début du test</DialogTitle>
            <DialogDescription asChild>
              <div className="w-full space-y-3 text-center font-semibold">
                <p className="">
                  {' '}
                  Vous êtes sur le point de débuter un test de {title} type
                  examen TCF CANADA. Il comporte {questions} questions et dure{' '}
                  {duration} minutes exactement.
                </p>
                <p className="">
                  Prenez la peine de bien lire les questions et les consignes
                  avant de répondre{' '}
                </p>
                <p className="text-base uppercase text-black">
                  Etes-vous prêt à commencer?
                </p>
              </div>
            </DialogDescription>
            <div className="flex w-full items-center justify-between">
              <DialogClose asChild>
                <Button variant={'secondary'}>Annuler</Button>
              </DialogClose>
              <Link href={`/examen/tcf/${serie.libelle}/${href}`}>
                <Button>Commencer</Button>
              </Link>
            </div>
          </DialogContent>
        </Dialog>
      ) : (
        <Card className="cursor-not-allowed overflow-hidden border border-blue-200">
          <CardContent className="bg-muted text-muted-foreground">
            <div className="flex h-[150px] w-[200px] flex-col items-center justify-center">
              <span className="h-20 w-20 text-zinc-500">{icon}</span>
              <p className="font-medium">{title}</p>
            </div>
            <div className="flex items-center gap-1.5">
              <Clock className="h-4 w-4" />
              <p>{duration} minutes</p>
            </div>
            <div className="flex items-center gap-1.5">
              <HelpCircle className="h-4 w-4" />
              <p>{questions} questions</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
