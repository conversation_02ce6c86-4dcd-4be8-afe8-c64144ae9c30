'use client';
import React, { useEffect, useRef, useState } from 'react';
import { cn, getTestSet } from '@/lib/utils';

import Image from 'next/image';
import Dialog from '@/components/Dialog';
import logo2 from '@/public/logo2.jpg';
import type { Session } from 'next-auth';
import { Serie } from '@/types';
import { EndTextBtn } from '../../_components/button';
import { toast as sonner, toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { saveTestEOUseCase } from '../actions';
import axios from 'axios';
import useCredits from '@/hooks/use-credits';
import { Button } from '@/components/ui/button';
import { convertFromBase64, convertToBase64 } from '../../utils';
import { useEOStore } from '@/context/ee-provider';
import { v4 as uuid } from 'uuid';
import { useIndexedDB, initDB } from 'react-indexed-db-hook';
import { delete<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TCFDBConfig } from '@/config/db';
import Taskeo1 from '../../_components/t1';
import Taskeo2 from '../../_components/t2';
import Taskeo3 from '../../_components/t3';
import { useSession } from 'next-auth/react';
import logger from '@/lib/logger';

async function retryUpload(file: File, token: string) {
  const formData = new FormData();
  formData.append('files', file);
  let attempt = 0;
  while (attempt < 3) {
    try {
      const { data } = await axios.post(
        `${process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || ''}/api/user-eofile/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
            Accept: '*',
            Authorization: `Bearer ${token}`,
          },
        },
      );
      const name = data.file.filename as string;
      logger.log(`%cUPLOAD SUCESSFUL :${name}`);
      return name;
    } catch (error) {
      // console.log(error);

      logger.error(`Upload attempt ${attempt + 1} failed with error: ${error}`);
      if (attempt == 3) {
        throw new Error(`Impossile d'uploader le fichier`);
      }
      attempt++;
    }
  }
}
const uploadSound = async ({
  f1,
  f2,
  f3,
  token,
}: {
  f1: File;
  f2: File;
  f3: File;
  token: string;
}) => {
  let taskUrl1: string | undefined;
  let taskUrl2: string | undefined;
  let taskUrl3: string | undefined;

  taskUrl1 = await retryUpload(f1, token);
  taskUrl2 = await retryUpload(f2, token);
  taskUrl3 = await retryUpload(f3, token);
  return taskUrl1 && taskUrl2 && taskUrl3
    ? { taskUrl1, taskUrl2, taskUrl3 }
    : null;
};

initDB(TCFDBConfig);

export default function TestEOTCF({
  session,
  serie,
}: {
  session: Session;
  serie: Serie;
}) {
  const db = useIndexedDB('testeo');
  const current = useEOStore((state) => state.current);
  const reset = useEOStore((state) => state.reset);
  const fileOne = useEOStore((state) => state.fileOne);
  const fileTwo = useEOStore((state) => state.fileTwo);
  const fileThree = useEOStore((state) => state.fileThree);
  const prevSetFileOne = useEOStore((state) => state.prevSetFileOne);
  const prevSetFileTwo = useEOStore((state) => state.prevSetFileTwo);
  const prevSetFileThree = useEOStore((state) => state.prevSetFileThree);
  const { update } = useSession();
  const [isShow, setIsShow] = useState(false);
  const hasChecked = useRef(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const btnref = useRef<HTMLButtonElement>(null);
  const stopBtn = useRef<HTMLButtonElement>(null);

  const testSet = getTestSet(serie);
  const taskOne = testSet?.EO?.[0].tasks.find((t) => t.numero == 82)!;
  const taskTwo = testSet?.EO?.[0].tasks.find((t) => t.numero == 83)!;
  const taskThree = testSet?.EO?.[0].tasks.find((t) => t.numero == 84)!;
  const { checkCredit } = useCredits();
  const router = useRouter();

  const SaveToLocalStorage = async ({
    liblle,
    userId,
    fileOne,
    fileTwo,
    fileThree,
  }: {
    liblle: string;
    userId: string;
    fileOne: File;
    fileTwo: File;
    fileThree: File;
  }) => {
    const storageKey = `TCF_EO_RES_${liblle}_${userId}`;
    const f1Base64 = await convertToBase64(fileOne);
    const f2Base64 = await convertToBase64(fileTwo);
    const f3Base64 = await convertToBase64(fileThree);
    const payload = {
      fileOne: f1Base64,
      fileTwo: f2Base64,
      fileThree: f3Base64,
    };
    const data = {
      key: storageKey,
      payload,
    };
    await db.add(data);
  };

  const sendTest = async () => {
    const data = await uploadSound({
      f1: fileOne!,
      f2: fileTwo!,
      f3: fileThree!,
      token: session.user.accessToken as string,
    });
    if (!data) throw new Error("Impossible d'uploader le fichier");
    await saveTestEOUseCase({
      payload: data,
      serie: serie._id,
    });
  };

  const onExpire = async () => {
    if (fileOne !== null && fileThree !== null && fileTwo !== null) {
      try {
        setIsSubmitting(true);
        await SaveToLocalStorage({
          liblle: serie.libelle,
          userId: session.user._id,
          fileOne,
          fileTwo,
          fileThree,
        });
        if (!(await checkCredit('TCF', 'EO'))) {
          setIsSubmitting(false);
          return;
        }

        sonner.promise(
          async () => {
            return await sendTest();
          },
          {
            loading: 'Envoie du test en cours...',
            success: () => {
              update({
                remains: {
                  remainTCF: {
                    balance_ee: session?.user.remains?.remainTCF?.balance_ee,
                    balance_eo:
                      session?.user.remains?.remainTCF?.balance_eo! - 1,
                    remain_day: session?.user.remains?.remainTCF?.remain_day,
                  },
                  remainTEF: session?.user.remains?.remainTEF,
                },
              });
              const storageKey = `TCF_EO_RES_${serie.libelle}_${session.user._id}`;
              const hasShownKey = `${storageKey}_shown`;
              deleteRecordByKey('TCF-EO', 'testeo', storageKey).then(() => {
                localStorage.removeItem(hasShownKey);
                router.back();
              });
              return "Vos productions audio ont bien été soumises. Vous serrez informé(e) lorsqu'un resultat sera disponible";
            },
            error: (error) => {
              setIsSubmitting(false);
              return error.message === 'Failed to fetch'
                ? 'Verifier votre connexion'
                : error.message;
            },
            closeButton: true,
            duration: 1000 * 20,
          },
        );
      } catch (error) {
        setIsSubmitting(false);
        console.log(error);
      }
    } else {
      reset();
      router.push(`/examen/tcf/${serie.libelle}`);
      sonner.warning(
        'Veuillez enregistrer vos 3 productions avant de soumettre',
        {
          description: 'Vous pourrez terminer votre test plus tard',
          duration: 1000 * 20,
          closeButton: true,
        },
      );
    }
  };

  const loadPrevTest = async () => {
    const storageKey = `TCF_EO_RES_${serie.libelle}_${session.user._id}`;
    const hasShownKey = `${storageKey}_shown`;
    const data = await db.getByIndex('key', storageKey);
    const payload = data ? data.payload : undefined;

    const f1 = await convertFromBase64(payload.fileOne as string, uuid());
    const f2 = await convertFromBase64(payload.fileTwo as string, uuid());
    const f3 = await convertFromBase64(payload.fileThree as string, uuid());

    prevSetFileOne(f1);
    prevSetFileTwo(f2);
    prevSetFileThree(f3);

    await deleteRecordByKey('TCF-EO', 'testeo', storageKey);
    localStorage.removeItem(hasShownKey);
  };

  useEffect(() => {
    if (!serie?.libelle || !session?.user?._id || isShow || hasChecked.current)
      return;

    const storageKey = `TCF_EO_RES_${serie.libelle}_${session.user._id}`;
    const hasShownKey = `${storageKey}_shown`; // Clé pour éviter les répétitions

    // Vérifier si le popup a déjà été affiché
    if (localStorage.getItem(hasShownKey)) return;
    hasChecked.current = true;
    db.getByIndex('key', storageKey).then((data: any) => {
      const payload = data ? data.payload : undefined;

      if (payload) {
        setIsShow(true);
        localStorage.setItem(hasShownKey, 'true'); // Marquer comme affiché

        toast.custom(
          (id) => (
            <EOPersitence
              id={id}
              message="Un de vos tests précédents a été détecté. Voulez-vous le charger ? Cliquer sur Non effacera le test."
              handleYes={loadPrevTest}
              handleNo={async () => {
                await deleteRecordByKey('TCF-EO', 'testeo', storageKey);
                localStorage.removeItem(hasShownKey);
              }}
            />
          ),
          {
            position: 'bottom-left',
            closeButton: true,
            duration: 1000 * 60 * 5,
          },
        );
      }
    });
  }, [serie, session, isShow]);

  return (
    <div className="relative flex w-full flex-col gap-5">
      <div className="fixed inset-x-0 top-0 z-40 flex w-full flex-row items-center justify-around border-b bg-white p-2 md:justify-center">
        <div className="p-2 tracking-wide md:tracking-wider">
          <p className="text-center text-xs font-semibold md:text-base">
            TCF: Objectif-Canada || Série {serie.libelle} || Expression orale.
          </p>
        </div>
        <EndTextBtn onClick={() => onExpire()}>
          <button
            disabled={
              isSubmitting ||
              fileOne === null ||
              fileTwo === null ||
              fileThree === null
            }
            title="stop"
            ref={stopBtn}
            className="flex aspect-video items-center justify-center rounded-sm bg-red-500 p-1 disabled:bg-red-300 md:p-3"
          >
            <p className="font-semibold text-white">Fin</p>
          </button>
        </EndTextBtn>
      </div>

      <div className="relative mx-auto mt-20 w-full flex-1 space-y-2 md:mt-14">
        <div className="flex items-center gap-3 p-2 text-xs md:text-sm">
          <span
            onClick={() => {
              if (current !== 1) btnref.current?.click();
            }}
            className={cn('rounded-md px-3 py-2 text-white', {
              'bg-blue-700 font-semibold': current == 1,
              'bg-blue-500/60 font-normal': !(current == 1),
            })}
          >
            Tâche 1
          </span>
          <span
            onClick={() => {
              if (current !== 2) btnref.current?.click();
            }}
            className={cn('rounded-md px-3 py-2 text-white', {
              'bg-blue-700 font-semibold': current == 2,
              'bg-blue-500/60 font-normal': !(current == 2),
            })}
          >
            Tâche 2
          </span>

          <span
            onClick={() => {
              if (current !== 3) btnref.current?.click();
            }}
            className={cn('rounded-md px-3 py-2 text-white', {
              'bg-blue-700 font-semibold': current == 3,
              'bg-blue-500/60 font-normal': !(current == 3),
            })}
          >
            Tâche 3
          </span>
        </div>
        <Image
          src={logo2}
          width={1277}
          height={1280}
          priority
          className="absolute right-2 top-2 hidden h-40 w-40 md:block"
          alt="logo"
        />
        {current == 1 ? <Taskeo1 stopBtn={stopBtn} task={taskOne} /> : null}
        {current == 2 ? <Taskeo2 stopBtn={stopBtn} task={taskTwo} /> : null}
        {current >= 3 ? <Taskeo3 stopBtn={stopBtn} task={taskThree} /> : null}
        <Dialog
          description="Vous ne pouvez pas passer d'une tâche à l'autre lors d'un test d'Expression orale"
          title="Info"
          ref={btnref}
          onClick={() => {}}
        />
      </div>
    </div>
  );
}

function EOPersitence({
  message,
  id,
  handleYes,
  handleNo,
}: {
  message: string;
  id: string | number;
  handleYes: () => Promise<void>;
  handleNo: () => void;
}) {
  const router = useRouter();
  return (
    <div className="flex flex-col gap-2 rounded-sm border bg-white p-3">
      <span className="p-0 text-center text-xl font-semibold text-red-500">
        Information !!!
      </span>
      <span className="max-w-[40ch] p-0 text-center text-base font-bold">
        {message}
      </span>

      <div className="flex items-center justify-between">
        <Button
          variant={'destructive'}
          className="uppercase"
          onClick={() => {
            handleNo();
            toast.dismiss(id);
            router.back();
          }}
        >
          Non
        </Button>
        <Button
          onClick={async () => {
            await handleYes();
            toast.dismiss(id);
          }}
          className="animate-buttonheartbeat bg-green-500 uppercase text-white hover:bg-green-300"
        >
          Oui
        </Button>
      </div>
    </div>
  );
}
