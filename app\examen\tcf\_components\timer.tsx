/* eslint-disable react-hooks/exhaustive-deps */
'use client';
import 'react-sweet-progress/lib/style.css';
import { useTimer } from 'react-timer-hook';
import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import Progress from '@/lib/jscomponent';

interface Props {
  expiryTimestamp: Date;
  onExpire: () => void;
  autoStart?: boolean;
  starting?: boolean;
  mode?: 'CE' | 'CO' | 'EE';
  serieLib?: string;
  recordTime?: boolean;
  totalduration: number;
}
export default function GlobalTimer({
  expiryTimestamp,
  onExpire,
  autoStart = true,
  starting,
  mode,
  serieLib,
  recordTime,
  totalduration,
}: Props) {
  const { data: session } = useSession();
  const [updatedDate, setUpdatedDate] = useState<Date>(expiryTimestamp);
  const [storageKey, setStorageKey] = useState<string>();
  useEffect(() => {
    if (typeof window === 'undefined' || !session) return;
    const key = `${mode}_TIME_${serieLib}_${session?.user?._id}`;
    setStorageKey(key);
    const storedData = JSON.parse(localStorage.getItem(key) ?? '{}');
    const storedTime = storedData?.time ?? 0;
    if (storedTime > 0) {
      const adjustedTime = storedTime < 15 ? storedTime + 30 : storedTime;
      const newDate = new Date();
      newDate.setSeconds(newDate.getSeconds() + adjustedTime);
      setUpdatedDate(newDate);
    } else {
      setUpdatedDate(expiryTimestamp);
    }
  }, [expiryTimestamp, mode, serieLib, session]);

  if (!storageKey) return null;

  return (
    <TimerBlock
      expiryTimestamp={updatedDate}
      onExpire={onExpire}
      autoStart={autoStart}
      starting={starting}
      recordTime={recordTime}
      totalduration={totalduration}
      storageKey={storageKey}
    />
  );
}

function TimerBlock({
  expiryTimestamp,
  onExpire,
  autoStart,
  starting,
  recordTime,
  totalduration,
  storageKey,
}: {
  expiryTimestamp: Date;
  onExpire: () => void;
  autoStart?: boolean;
  starting?: boolean;
  recordTime?: boolean;
  totalduration: number;
  storageKey: string;
}) {
  const { seconds, minutes, totalSeconds, start, pause } = useTimer({
    expiryTimestamp,
    onExpire,
    autoStart,
  });

  useEffect(() => {
    if (starting) start();
    else pause();
  }, [starting]);

  useEffect(() => {
    if (recordTime && storageKey) {
      localStorage.setItem(storageKey, JSON.stringify({ time: totalSeconds }));
    }
  }, [totalSeconds]);

  const remainingPercentage = (totalSeconds / totalduration) * 100;

  const theme = {
    error: {
      symbol: ' ',
      trailColor: '',
      color: 'red',
    },
    default: {
      symbol: ' ',
      trailColor: '',
      color: 'blue',
    },
    active: {
      symbol: ' ',
      trailColor: '',
      color: 'orange',
    },
    success: {
      symbol: ' ',
      trailColor: '',
      color: 'green',
    },
  };

  const status = (percent: number): string => {
    let res = '';
    if (percent >= 67) {
      res = 'success';
    }

    if (percent < 67 && percent >= 34) {
      res = 'active';
    }

    if (percent < 34) {
      res = 'error';
    }

    return res;
  };

  return (
    <div className="flex w-full items-center gap-2">
      <div className="flex items-center">
        <Digit value={minutes} />
        <span className="text-sm text-gray-400">:</span>
        <Digit value={seconds} />
      </div>
      <div className="w-[200px]">
        <Progress
          theme={theme}
          percent={remainingPercentage}
          status={status(remainingPercentage)}
        />
      </div>
    </div>
  );
}

const Digit = ({ value }: { value: number }) => {
  const leftDigit = value >= 10 ? value.toString()[0] : '0';
  const rightDigit = value >= 10 ? value.toString()[1] : value.toString();
  return (
    <div>
      <span>{leftDigit}</span>
      <span>{rightDigit}</span>
    </div>
  );
};
