import { fechSerieTEF } from '@/app/examen/actions';
import { getAuthSession } from '@/lib/auth';
import { notFound } from 'next/navigation';
import React from 'react';
import TestCETEF from '../../../_components/testCE';
import { countQ } from '@/app/examen/utils';
interface Props {
  params: {
    slug: string;
  };
}
async function Page({ params: { slug } }: Props) {
  const session = await getAuthSession();

  const serie = await fechSerieTEF(
    slug,
    session?.user.accessToken || '',
    'COM',
  );
  if (!serie) return notFound();
  // changer en fonction de la disponibilité du test en <
  if (countQ(serie.ceQuestions.questions) < serie.ceQuestions.total) {
    return (
      <section>
        <h1>Test not available</h1>
      </section>
    );
  }

  return (
    <section className="w-full">
      <TestCETEF serie={serie} />
    </section>
  );
}

export default Page;
