import { fechSerieTEF } from '@/app/examen/actions';
import { getAuthSession } from '@/lib/auth';
import { notFound, redirect } from 'next/navigation';
import React from 'react';
import TestEOTEF from '../../../_components/testEO';
import EOProvider from '@/context/ee-provider';
interface Props {
  params: {
    slug: string;
  };
}
async function Page({ params: { slug } }: Props) {
  const session = await getAuthSession();
  if (!session) redirect('/auth');
  const serie = await fechSerieTEF(slug, session.user.accessToken, 'EE');

  if (!serie) return notFound();
  if (
    serie.eoQuestions.length <= 0 ||
    serie.eoQuestions[0].sections.length < 2
  ) {
    return (
      <section>
        <h1>Test not available</h1>
      </section>
    );
  }

  return (
    <section className="mt-5 w-full">
      <EOProvider initialCurrent={1}>
        <TestEOTEF serie={serie} session={session} />
      </EOProvider>
    </section>
  );
}

export default Page;
