import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Clock, HelpCircle } from 'lucide-react';
import React from 'react';

function Loading() {
  return (
    <section className="container mt-4 flex h-full flex-col items-center justify-center gap-5 md:p-0">
      <h1 className="flex flex-wrap items-center text-center text-lg">
        Vous êtes sur le point de commencer la série{' '}
        <Skeleton className="mx-2 h-4 w-10 bg-blue-300" /> du TEF nouveau format
      </h1>
      <p className="prose text-center">
        Vous devez choisir une discipline pour débuter le test, bon
        apprentissage !
      </p>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="overflow-hidden border border-blue-200">
          <CardContent className="bg-zinc-50 text-slate-700">
            <div className="flex h-[150px] w-[200px] flex-col items-center justify-center">
              <Skeleton className="h-20 w-20 bg-blue-300" />
              <p className="font-medium">Comprehension ecrite</p>
            </div>
            <div className="flex items-center gap-1.5">
              <Clock className="h-4 w-4" />
              <Skeleton className="h-3 w-full bg-blue-300" />
            </div>
            <div className="flex items-center gap-1.5">
              <HelpCircle className="h-4 w-4" />
              <Skeleton className="h-3 w-20 bg-blue-300" />
            </div>
          </CardContent>
        </Card>
        <Card className="overflow-hidden border border-blue-200">
          <CardContent className="bg-zinc-50 text-slate-700">
            <div className="flex h-[150px] w-[200px] flex-col items-center justify-center">
              <Skeleton className="h-20 w-20 bg-blue-300" />
              <p className="font-medium">Comprehension orale</p>
            </div>
            <div className="flex items-center gap-1.5">
              <Clock className="h-4 w-4" />
              <Skeleton className="h-3 w-full bg-blue-300" />
            </div>
            <div className="flex items-center gap-1.5">
              <HelpCircle className="h-4 w-4" />
              <Skeleton className="h-3 w-20 bg-blue-300" />
            </div>
          </CardContent>
        </Card>
        <Card className="overflow-hidden border border-blue-200">
          <CardContent className="bg-zinc-50 text-slate-700">
            <div className="flex h-[150px] w-[200px] flex-col items-center justify-center">
              <Skeleton className="h-20 w-20 bg-blue-300" />
              <p className="font-medium">Production ecrite</p>
            </div>
            <div className="flex items-center gap-1.5">
              <Clock className="h-4 w-4" />
              <Skeleton className="h-3 w-full bg-blue-300" />
            </div>
            <div className="flex items-center gap-1.5">
              <HelpCircle className="h-4 w-4" />
              <Skeleton className="h-3 w-20 bg-blue-300" />
            </div>
          </CardContent>
        </Card>
        <Card className="overflow-hidden border border-blue-200">
          <CardContent className="bg-zinc-50 text-slate-700">
            <div className="flex h-[150px] w-[200px] flex-col items-center justify-center">
              <Skeleton className="h-20 w-20 bg-blue-300" />
              <p className="font-medium">Production orale</p>
            </div>
            <div className="flex items-center gap-1.5">
              <Clock className="h-4 w-4" />
              <Skeleton className="h-3 w-full bg-blue-300" />
            </div>
            <div className="flex items-center gap-1.5">
              <HelpCircle className="h-4 w-4" />
              <Skeleton className="h-3 w-20 bg-blue-300" />
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}

export default Loading;
