import { getAuthSession } from '@/lib/auth';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { notFound, redirect } from 'next/navigation';
import React from 'react';
import { fechSerieTEF } from '../../actions';
import { buttonVariants } from '@/components/ui/button';
import {
  ComprehensionEcriteCard,
  ComprehensionOraleCard,
  ProductionEcritCard,
  ProductionOraleCard,
} from '../_components/card';
import { getFreeSerrie } from '@/lib/free-serie-helper';
interface Props {
  params: {
    slug: string;
  };
}

async function Page({ params: { slug } }: Props) {
  const session = await getAuthSession();

  const FREE_TEF = await getFreeSerrie('tef');
  if (!session && !FREE_TEF.includes(slug)) redirect('/auth');

  const exist = await fechSerieTEF(
    slug,
    session?.user.accessToken || '',
    'COM',
  );
  if (!exist) return notFound();

  return (
    <section className="relative mt-1 flex h-full flex-col items-center justify-center gap-5 md:container md:p-0">
      <Link
        href={'/examen/tef'}
        className={buttonVariants({
          variant: 'outline',
          size: 'sm',
          className: 'absolute bottom-2 right-2 w-fit sm:left-3 sm:top-3',
        })}
      >
        <ArrowLeft className="h-5 w-5 text-blue-500" />
      </Link>
      <h1 className="flex flex-wrap items-center text-center text-lg">
        Vous êtes sur le point de commencer la série{' '}
        <strong className="mx-2">{slug}</strong> du TEF nouveau format
      </h1>
      <p className="prose text-center">
        Vous devez choisir une discipline pour débuter le test, bon
        apprentissage !
      </p>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <ComprehensionEcriteCard serie={exist} />
        <ComprehensionOraleCard serie={exist} />
        <ProductionEcritCard serie={exist} />
        <ProductionOraleCard serie={exist} />
      </div>
    </section>
  );
}

export default Page;
