'use client';
import { Card, CardContent } from '@/components/ui/card';
import { Clock, HelpCircle } from 'lucide-react';
import Link from 'next/link';
import React from 'react';
import { countQ } from '../../utils';
import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { DialogClose } from '@radix-ui/react-dialog';

const ComprehensionEcriteCard = ({ serie }: { serie: SerieTEF }) => {
  // changer en fonction de la disponibilité du test en <
  return (
    <div className="cursor-pointer">
      {countQ(serie.ceQuestions.questions) < serie.ceQuestions.total ? (
        <Card className={'overflow-hidden border border-blue-200'}>
          <CardContent className="bg-muted text-muted-foreground">
            <div className="flex h-[150px] w-[200px] flex-col items-center justify-center">
              <span className="h-20 w-20">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="h-full w-full text-zinc-500"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"
                  />
                </svg>
              </span>
              <p className="font-medium">Comprehension ecrite</p>
            </div>
            <div className="flex items-center gap-1.5">
              <Clock className="h-4 w-4" />
              <p>60 minutes</p>
            </div>
            <div className="flex items-center gap-1.5">
              <HelpCircle className="h-4 w-4" />
              <p>{serie.ceQuestions.total} questions</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Dialog>
          <DialogTrigger>
            <Card className="overflow-hidden border border-blue-200">
              <CardContent className="bg-zinc-50 text-slate-700">
                <div className="flex h-[150px] w-[200px] flex-col items-center justify-center">
                  <span className="h-20 w-20">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="h-full w-full text-blue-500"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"
                      />
                    </svg>
                  </span>
                  <p className="font-medium">Comprehension ecrite</p>
                </div>
                <div className="flex items-center gap-1.5">
                  <Clock className="h-4 w-4" />
                  <p> {serie.ceQuestions.duree || 60} minutes</p>
                </div>
                <div className="flex items-center gap-1.5">
                  <HelpCircle className="h-4 w-4" />
                  <p>{serie.ceQuestions.total} questions</p>
                </div>
              </CardContent>
            </Card>
          </DialogTrigger>
          <DialogContent>
            <DialogTitle>Début du test</DialogTitle>
            <DialogDescription>
              <div className="w-full space-y-3 text-center font-semibold">
                <p className="">
                  {' '}
                  Vous êtes sur le point de débuter un test de Comprehension
                  ecrite type examen TEF CANADA. Il comporte{' '}
                  {serie.ceQuestions.total} questions et dure{' '}
                  {serie.ceQuestions.duree || 60} minutes exactement.
                </p>
                <p className="">
                  Prenez la peine de bien lire les questions et les consignes
                  avant de répondre{' '}
                </p>
                <p className="text-base uppercase text-black">
                  Etes-vous prêt à commencer?
                </p>
              </div>
            </DialogDescription>
            <div className="flex w-full items-center justify-between">
              <DialogClose>
                <Button variant={'secondary'}>Annuler</Button>
              </DialogClose>

              <Link href={`/examen/tef/${serie.libelle}/comprehension-ecrite`}>
                <Button>Commencer</Button>
              </Link>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

const ComprehensionOraleCard = ({ serie }: { serie: SerieTEF }) => {
  return (
    <>
      {countQ(serie.coQuestions.questions) < serie.coQuestions.total ? (
        <Card className="overflow-hidden border border-blue-200">
          <CardContent className="bg-muted text-muted-foreground">
            <div className="flex h-[150px] w-[200px] flex-col items-center justify-center">
              <span className="h-20 w-20">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="h-full w-full text-zinc-500"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M19.114 5.636a9 9 0 0 1 0 12.728M16.463 8.288a5.25 5.25 0 0 1 0 7.424M6.75 8.25l4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.009 9.009 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z"
                  />
                </svg>
              </span>
              <p className="font-medium">Comprehension orale</p>
            </div>
            <div className="flex items-center gap-1.5">
              <Clock className="h-4 w-4" />
              <p>{serie.coQuestions.duree} minutes</p>
            </div>
            <div className="flex items-center gap-1.5">
              <HelpCircle className="h-4 w-4" />
              <p>{serie.coQuestions.total} questions</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Dialog>
          <DialogTrigger>
            <Card className="overflow-hidden border border-blue-200">
              <CardContent className="bg-zinc-50 text-slate-700">
                <div className="flex h-[150px] w-[200px] flex-col items-center justify-center">
                  <span className="h-20 w-20">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="h-full w-full text-blue-500"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M19.114 5.636a9 9 0 0 1 0 12.728M16.463 8.288a5.25 5.25 0 0 1 0 7.424M6.75 8.25l4.72-4.72a.75.75 0 0 1 1.28.53v15.88a.75.75 0 0 1-1.28.53l-4.72-4.72H4.51c-.88 0-1.704-.507-1.938-1.354A9.009 9.009 0 0 1 2.25 12c0-.83.112-1.633.322-2.396C2.806 8.756 3.63 8.25 4.51 8.25H6.75Z"
                      />
                    </svg>
                  </span>
                  <p className="font-medium">Comprehension orale</p>
                </div>
                <div className="flex items-center gap-1.5">
                  <Clock className="h-4 w-4" />
                  <p>{serie.coQuestions.duree} minutes</p>
                </div>
                <div className="flex items-center gap-1.5">
                  <HelpCircle className="h-4 w-4" />
                  <p>{serie.coQuestions.total} questions</p>
                </div>
              </CardContent>
            </Card>
          </DialogTrigger>
          <DialogContent>
            <DialogTitle>Début du test</DialogTitle>
            <DialogDescription>
              <div className="w-full space-y-3 text-center font-semibold">
                <p className="">
                  {' '}
                  Vous êtes sur le point de débuter un test de Comprehension
                  orale type examen TEF CANADA. Il comporte{' '}
                  {serie.coQuestions.total} questions et dure{' '}
                  {serie.coQuestions.duree} minutes exactement.
                </p>
                <p className="">
                  Prenez la peine de bien lire les questions et les consignes
                  avant de répondre{' '}
                </p>
                <p className="text-base uppercase text-black">
                  Etes-vous prêt à commencer?
                </p>
              </div>
            </DialogDescription>
            <div className="flex w-full items-center justify-between">
              <DialogClose>
                <Button variant={'secondary'}>Annuler</Button>
              </DialogClose>
              <Link href={`/examen/tef/${serie.libelle}/comprehension-orale`}>
                <Button>Commencer</Button>
              </Link>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  );
};

const ProductionEcritCard = ({ serie }: { serie: SerieTEF }) => {
  return (
    <>
      {serie?.eeQuestions?.length > 0 &&
      serie?.eeQuestions?.[0].sections.length >= 2 ? (
        <Dialog>
          <DialogTrigger>
            <Card className="overflow-hidden border border-blue-200">
              <CardContent className="bg-zinc-50 text-slate-700">
                <div className="flex h-[150px] w-[200px] flex-col items-center justify-center">
                  <span className="h-20 w-20">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="h-full w-full text-blue-500"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                      />
                    </svg>
                  </span>
                  <p className="font-medium">Production ecrite</p>
                </div>
                <div className="flex items-center gap-1.5">
                  <Clock className="h-4 w-4" />
                  <p>60 minutes</p>
                </div>
                <div className="flex items-center gap-1.5">
                  <HelpCircle className="h-4 w-4" />
                  <p>
                    {serie.eeQuestions?.[0]?.sections?.length || 0} sections
                  </p>
                </div>
              </CardContent>
            </Card>
          </DialogTrigger>
          <DialogContent>
            <DialogTitle>Début du test</DialogTitle>
            <DialogDescription>
              <div className="w-full space-y-3 text-center font-semibold">
                <p className="">
                  {' '}
                  Vous êtes sur le point de débuter un test de Production ecrite
                  type examen TEF CANADA. Il comporte{' '}
                  {serie.eeQuestions?.[0]?.sections?.length || 0} sections et
                  dure {60} minutes exactement.
                </p>
                <p className="">
                  Prenez la peine de bien lire les questions et les consignes
                  avant de répondre{' '}
                </p>
                <p className="text-base uppercase text-black">
                  Etes-vous prêt à commencer?
                </p>
              </div>
            </DialogDescription>
            <div className="flex w-full items-center justify-between">
              <DialogClose>
                <Button variant={'secondary'}>Annuler</Button>
              </DialogClose>

              <Link href={`/examen/tef/${serie.libelle}/production-ecrite`}>
                <Button>Commencer</Button>
              </Link>
            </div>
          </DialogContent>
        </Dialog>
      ) : (
        <Card className="cursor-not-allowed overflow-hidden border border-blue-200">
          <CardContent className="bg-muted text-muted-foreground">
            <div className="flex h-[150px] w-[200px] flex-col items-center justify-center">
              <span className="h-20 w-20">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="h-full w-full text-zinc-500"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                  />
                </svg>
              </span>
              <p className="font-medium">Production ecrite</p>
            </div>
            <div className="flex items-center gap-1.5">
              <Clock className="h-4 w-4" />
              <p>60 minutes</p>
            </div>
            <div className="flex items-center gap-1.5">
              <HelpCircle className="h-4 w-4" />
              <p>{serie.eeQuestions?.[0]?.sections?.length || 0} sections</p>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
};

const ProductionOraleCard = ({ serie }: { serie: SerieTEF }) => {
  return (
    <>
      {serie?.eoQuestions?.length > 0 &&
      serie?.eoQuestions?.[0].sections.length >= 2 ? (
        <Dialog>
          <DialogTrigger>
            <Card className="overflow-hidden border border-blue-200">
              <CardContent className="bg-zinc-50 text-slate-700">
                <div className="flex h-[150px] w-[200px] flex-col items-center justify-center">
                  <span className="h-20 w-20">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      strokeWidth={1.5}
                      stroke="currentColor"
                      className="h-full w-full text-blue-500"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M12 18.75a6 6 0 0 0 6-6v-1.5m-6 7.5a6 6 0 0 1-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 0 1-3-3V4.5a3 3 0 1 1 6 0v8.25a3 3 0 0 1-3 3Z"
                      />
                    </svg>
                  </span>
                  <p className="font-medium">Production orale</p>
                </div>
                <div className="flex items-center gap-1.5">
                  <Clock className="h-4 w-4" />
                  <p>15 minutes</p>
                </div>
                <div className="flex items-center gap-1.5">
                  <HelpCircle className="h-4 w-4" />
                  <p>
                    {serie.eoQuestions?.[0]?.sections?.length || 0} sections
                  </p>
                </div>
              </CardContent>
            </Card>
          </DialogTrigger>
          <DialogContent>
            <DialogTitle>Début du test</DialogTitle>
            <DialogDescription>
              <div className="w-full space-y-3 text-center font-semibold">
                <p className="">
                  {' '}
                  Vous êtes sur le point de débuter un test de Production orale
                  type examen TEF CANADA. Il comporte{' '}
                  {serie.eoQuestions?.[0]?.sections?.length || 0} sections et
                  dure {15} minutes exactement.
                </p>
                <p className="">
                  Prenez la peine de bien lire les questions et les consignes
                  avant de répondre{' '}
                </p>
                <p className="text-base uppercase text-black">
                  Etes-vous prêt à commencer?
                </p>
              </div>
            </DialogDescription>
            <div className="flex w-full items-center justify-between">
              <DialogClose>
                <Button variant={'secondary'}>Annuler</Button>
              </DialogClose>

              <Link href={`/examen/tef/${serie.libelle}/production-orale`}>
                <Button>Commencer</Button>
              </Link>
            </div>
          </DialogContent>
        </Dialog>
      ) : (
        <Card className="cursor-not-allowed overflow-hidden border border-blue-200">
          <CardContent className="bg-muted text-muted-foreground">
            <div className="flex h-[150px] w-[200px] flex-col items-center justify-center">
              <span className="h-20 w-20">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="h-full w-full text-zinc-500"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 18.75a6 6 0 0 0 6-6v-1.5m-6 7.5a6 6 0 0 1-6-6v-1.5m6 7.5v3.75m-3.75 0h7.5M12 15.75a3 3 0 0 1-3-3V4.5a3 3 0 1 1 6 0v8.25a3 3 0 0 1-3 3Z"
                  />
                </svg>
              </span>
              <p className="font-medium">Production orale</p>
            </div>
            <div className="flex items-center gap-1.5">
              <Clock className="h-4 w-4" />
              <p>15 minutes</p>
            </div>
            <div className="flex items-center gap-1.5">
              <HelpCircle className="h-4 w-4" />
              <p>{serie.eoQuestions?.[0]?.sections?.length || 0} sections</p>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  );
};

export {
  ProductionEcritCard,
  ProductionOraleCard,
  ComprehensionEcriteCard,
  ComprehensionOraleCard,
};
