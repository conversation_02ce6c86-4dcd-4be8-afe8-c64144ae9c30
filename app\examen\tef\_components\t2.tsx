'use client';

import UseTimerDemo from '@/components/timer/Time';
import { v4 as uuid } from 'uuid';
import {
  forwardRef,
  RefObject,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { Mic, Play, RefreshCcw, Square } from 'lucide-react';
import {
  BUCKET_BASE_URL,
  EO_SECTION_A_TIME,
  EO_SECTION_B_FILENAME,
  EO_SECTION_B_TIME,
} from '@/config';
import { Button } from '@/components/ui/button';
import parse from 'html-react-parser';
import ImageZoomInOut from '@/components/ZoumInOutImage';
import logo from '@/public/logo4.png';
import { useEOStore } from '@/context/ee-provider';
import { toast } from 'sonner';
import { LiveAudioVisualizer } from 'react-audio-visualize';

export interface TEFTaskEO2Ref {
  stopRecording: () => void;
}

interface Props {
  task: SectionEO;
  stopBtn: RefObject<HTMLButtonElement>;
}

const TEFTaskEO2 = forwardRef<TEFTaskEO2Ref, Props>((props, ref) => {
  const { stopBtn, task } = props;
  const current = useEOStore((state) => state.current);
  const next = useEOStore((state) => state.next);
  const setFile = useEOStore((state) => state.setFileTwo);

  const [isSoundLoading, setIsSoundLoading] = useState<boolean>(true);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [starting, setStarting] = useState<boolean>(false);
  const [recording, setRecording] = useState(false);
  const [isLoadErrod, setIsLoadErrod] = useState(false);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const recordedChunksRef = useRef<Blob[]>([]);
  const [isPaused, setIsPaused] = useState<boolean>(false);
  const audioref = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    navigator.mediaDevices.getUserMedia({ audio: true }).then((stream) => {
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          recordedChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(recordedChunksRef.current, {
          type: 'audio/mp3',
        });
        const file = new File([blob], `${uuid()}.mp3`, { type: 'audio/mp3' });
        setFile(file);
        setRecording(false);
        setStarting(false);
        stopBtn.current?.click();
        stream.getTracks().forEach((track) => track.stop());
      };
    });
  }, []);

  const startRecording = () => {
    recordedChunksRef.current = [];
    mediaRecorderRef.current?.start();
    setRecording(true);
  };

  const stopRecording = () => {
    mediaRecorderRef.current?.stop();
    setRecording(false);
    setStarting(false);
  };

  const togglePause = () => {
    if (mediaRecorderRef.current) {
      if (isPaused) {
        mediaRecorderRef.current.resume();
        setRecording(true);
      } else {
        mediaRecorderRef.current.pause();
        setRecording(false);
      }
      setIsPaused(!isPaused);
    }
  };

  const date = new Date();
  const time = current == 1 ? EO_SECTION_A_TIME : EO_SECTION_B_TIME;
  date.setSeconds(date.getSeconds() + time);

  useEffect(() => {
    if (audioref) {
      audioref.current?.addEventListener('ended', () => {
        document?.getElementById('scroll')?.scrollIntoView();
        startRecording();
        setStarting(true);
      });
    }
    return;
  }, [audioref]);

  useImperativeHandle(ref, () => ({
    stopRecording() {
      startRecording();
    },
  }));

  return (
    <div className="flex h-fit flex-col items-center justify-center gap-1 pt-1 md:px-8">
      <div className="flex w-full flex-col gap-3 text-center font-semibold md:flex-row md:justify-between">
        <div className="mx-auto flex flex-col items-center justify-center space-y-4">
          <UseTimerDemo
            expiryTimestamp={date}
            onExpire={async () => {
              stopRecording();
            }}
            autoStart={false}
            starting={starting}
          />
          <h1 className="text-base capitalize underline underline-offset-4">
            {task.libelle}
          </h1>
          <div className="prose prose-base mt-5 max-w-[50ch] text-justify font-normal">
            {parse(task.consigne)}
          </div>
          <audio
            ref={audioref}
            src={`/audios/${EO_SECTION_B_FILENAME}`}
            onCanPlay={() => {
              setIsSoundLoading(false);
              setIsLoadErrod(false);
            }}
            onLoadedMetadata={() => {
              setIsSoundLoading(false);
              setIsLoadErrod(false);
            }}
            onError={() => {
              if (!isLoadErrod) {
                setIsLoadErrod(true);
                toast.error(
                  "Impossible de charger l'audio, veuillez réessayer.",
                  { duration: 5000, position: 'top-center' },
                );
              } else {
                setIsLoadErrod(false);
                toast.warning(
                  "Impossible de charger l'audio, lisez la description votre enregistrement va debuter dans 10s.",
                  { duration: 5000, position: 'top-center' },
                );
                setTimeout(() => {
                  startRecording();
                  setStarting(true);
                }, 10000);
              }
            }}
            className="Z-0 hidden"
          ></audio>
          <div className="flex items-center gap-3">
            <Button
              onClick={() => {
                if (!isPlaying) {
                  audioref.current?.play();
                  setIsPlaying(true);
                }
              }}
              disabled={isSoundLoading || isPlaying}
            >
              Ecoutez la consigne <Play className="ml-2 h-5 w-5 text-white" />
            </Button>
            {isLoadErrod && (
              <Button
                size={'icon'}
                variant={'outline'}
                onClick={() => audioref.current?.load()}
              >
                <RefreshCcw className="h-5 w-5 text-blue-500" />
              </Button>
            )}
          </div>
        </div>

        <div className="md:w-[50%]">
          {task.images?.map((img, i) => (
            <ImageZoomInOut
              imageUrl={
                img.trim().length > 0 ? `${BUCKET_BASE_URL}/${img}` : logo
              }
              placeholder={img.trim().length > 0 ? 'empty' : 'blur'}
              key={i}
            />
          ))}
        </div>
      </div>

      <div className="mt-5 flex w-full flex-col items-center justify-center gap-1">
        <div className="flex w-full flex-col items-center justify-center gap-1">
          <h1 className="text-lg font-semibold">Enregistrement</h1>
          {mediaRecorderRef.current != undefined ? (
            <LiveAudioVisualizer
              mediaRecorder={mediaRecorderRef.current}
              width={200}
              height={75}
            />
          ) : null}
          <div className="mx-auto flex w-fit items-center justify-between gap-1">
            <Button
              disabled={!starting}
              size={'icon'}
              onClick={() => {
                if (recording && !isPaused) {
                  stopRecording();
                } else {
                  startRecording();
                }
              }}
              className={`group h-[50px] w-[50px] rounded-full bg-red-500 hover:bg-zinc-100`}
            >
              {recording && !isPaused ? (
                <Square className="h-6 w-6 text-white group-hover:text-red-500" />
              ) : (
                <Mic className="h-6 w-6 text-white group-hover:text-red-500" />
              )}
            </Button>
            <Button disabled={!starting} onClick={togglePause}>
              {isPaused ? 'Reprendre' : 'Pause'}
            </Button>
          </div>
        </div>
      </div>

      <div id="scroll"></div>
    </div>
  );
});

TEFTaskEO2.displayName = 'TEFTaskEO2';
export default TEFTaskEO2;
