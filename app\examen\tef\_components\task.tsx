'use client';
import logo from '@/public/logo4.png';
import parse from 'html-react-parser';
import { useState } from 'react';
import { BUCKET_BASE_URL } from '@/config';
import EditorTEF from './editor';
import { Button } from '@/components/ui/button';
import ImageZoomInOut from '@/components/ZoumInOutImage';

type TaskType = {
  _id: string;
  numero: number;
  libelle: string;
  typeProduction: string;
  consigne: string;
  images: string[] | null;
  minWord: number;
  maxWord: number;
};
interface Props {
  num: number;
  text: string;
  task: TaskType;
}

export default function Task({ num, text, task }: Props) {
  const [showConsigne, setShowConsigne] = useState(true);

  const handleEyes = () => {
    const newSate = !showConsigne;
    setShowConsigne(newSate);
  };

  return (
    <div className="flex h-fit w-full flex-col items-start justify-around gap-5 pt-4 md:px-8">
      <div className="flex w-full flex-col gap-1 text-center font-semibold">
        {showConsigne ? (
          <LibelleAndConsigne task={task} />
        ) : (
          <>
            {task.images?.map((img, i) => (
              <ImageZoomInOut
                imageUrl={
                  img.trim().length > 0 ? `${BUCKET_BASE_URL}/${img}` : logo
                }
                placeholder={img.trim().length > 0 ? 'empty' : 'blur'}
                key={i}
              />
            ))}
          </>
        )}

        {task.images?.length && task.images?.length > 0 ? (
          <Button className="mx-auto my-2 max-w-sm" onClick={handleEyes}>
            {showConsigne
              ? 'Afficher les pieces jointes'
              : 'Afficher la consigne'}
          </Button>
        ) : null}
      </div>
      <div className="w-full">
        <EditorTEF num={num} text={text} />
      </div>
    </div>
  );
}

const LibelleAndConsigne = ({ task }: { task: TaskType }) => {
  return (
    <div>
      <h1 className="text-base capitalize underline underline-offset-4">
        {task.libelle}
      </h1>
      <p>{task.minWord} Mots minimum</p>
      <p>{task.maxWord} Mots maximum</p>
      <div className="prose mx-auto mt-5 max-w-5xl text-justify font-normal">
        {task.numero == 41 ? (
          <>
            <p>Voici le début d&apos;un article de presse</p>
            <blockquote>{parse(task.consigne)}</blockquote>
            <p>
              Terminez cet article :
              <ul>
                <li>en ajoutant à la suite un texte de 80 mots minimum.</li>
                <li>en faisant plusieurs paragraphes.</li>
              </ul>
            </p>
          </>
        ) : (
          <>
            <p>Vous avez lu dans un journal l&apos;affirmation suivante :</p>
            <blockquote>{parse(task.consigne)}</blockquote>
            <p>
              <ul>
                <li>
                  Écrivez une lettre au journal pour dire ce que vous en pensez.
                  (200 mots minimum)
                </li>
                <li>
                  Développez au moins trois arguments pour défendre votre point
                  de vue.
                </li>
              </ul>
            </p>
          </>
        )}
      </div>
    </div>
  );
};
