'use client';

import UseTimerDemo from '@/components/timer/Time';
import { v4 as uuid } from 'uuid';
import {
  forwardRef,
  RefObject,
  startTransition,
  useEffect,
  useImperativeHandle,
  useState,
} from 'react';
import { useVoiceVisualizer, VoiceVisualizer } from 'react-voice-visualizer';
import { Play } from 'lucide-react';
import {
  BUCKET_BASE_URL,
  EO_SECTION_A_FILENAME,
  EO_SECTION_A_TIME,
  EO_SECTION_B_FILENAME,
  EO_SECTION_B_TIME,
} from '@/config';
import { Button } from '@/components/ui/button';
import parse from 'html-react-parser';
import ImageZoomInOut from '@/components/ZoumInOutImage';
import logo from '@/public/logo4.png';
import useSound from 'use-sound';
import { useEOStore } from '@/context/ee-provider';
import { toast } from 'sonner';

const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
const fileType = isSafari ? 'audio/wav' : 'audio/mp3';

export interface TaskEORef {
  stopRecording: () => void;
}

interface Props {
  num: number;
  task: SectionEO;
  stopBtn: RefObject<HTMLButtonElement>;
}

const TaskEO = forwardRef<TaskEORef, Props>((props, ref) => {
  const { num, stopBtn, task } = props;
  const current = useEOStore((state) => state.current);
  const next = useEOStore((state) => state.next);
  const setFileTwo = useEOStore((state) => state.setFileTwo);
  const setFileOne = useEOStore((state) => state.setFileOne);
  const fileOne = useEOStore((state) => state.fileOne);
  const fileTwo = useEOStore((state) => state.fileTwo);
  const prevFileOne = useEOStore((state) => state.prevFileOne);
  const prevFileTwo = useEOStore((state) => state.prevFileTwo);
  const prevSetFileOne = useEOStore((state) => state.prevSetFileOne);
  const prevSetFileTwo = useEOStore((state) => state.prevSetFileTwo);
  const [isSoundLoading, setIsSoundLoading] = useState<boolean>(true);
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [starting, setstarting] = useState<boolean>(false);
  const [triggerEnd, setTriggerEnd] = useState(false);
  const fileName = current == 1 ? EO_SECTION_A_FILENAME : EO_SECTION_B_FILENAME;
  const [play, { sound }] = useSound(`/audios/${fileName}`, {
    onload() {
      setIsSoundLoading(false);
    },
    html5: true,
  });
  const recorderControls = useVoiceVisualizer();
  const { recordedBlob, audioRef, stopRecording, startRecording } =
    recorderControls;

  useEffect(() => {
    if (triggerEnd) {
      stopBtn.current?.click();
    }
  }, [triggerEnd]);

  useEffect(() => {
    if (!recordedBlob) return;
    const file = new File(
      [recordedBlob],
      `${uuid()}.${isSafari ? 'wav' : 'mp3'}`,
      {
        type: fileType,
      },
    );

    switch (num) {
      case 1:
        setFileOne(file);
        sound?.unload();
        next();
        break;
      case 2:
        setFileTwo(file);
        startTransition(() => {
          sound?.unload();
          setstarting(false);
          stopBtn.current?.click();
        });
        break;
      default:
        break;
    }
  }, [recordedBlob, num]);

  const date = new Date();
  const time = current == 1 ? EO_SECTION_A_TIME : EO_SECTION_B_TIME;
  date.setSeconds(date.getSeconds() + time);

  useEffect(() => {
    if (sound) {
      sound?.on('end', () => {
        document?.getElementById('scroll')?.scrollIntoView();
        startRecording();
        setstarting(true);
      });
    }
  }, [sound]);

  useImperativeHandle(ref, () => ({
    stopRecording() {
      startRecording();
    },
  }));

  const canShow = (current: number) => {
    switch (current) {
      case 1:
        return fileOne || starting;
      case 2:
        return fileTwo || starting;
      default:
        return starting;
    }
  };

  return (
    <div className="flex h-fit flex-col items-center justify-center gap-1 pt-1 md:px-8">
      <div className="flex w-full flex-col gap-3 text-center font-semibold md:flex-row md:justify-between">
        <div className="mx-auto flex flex-col items-center justify-center space-y-4">
          <UseTimerDemo
            expiryTimestamp={date}
            onExpire={async () => {
              stopRecording();
            }}
            autoStart={false}
            starting={starting}
          />
          <h1 className="text-base capitalize underline underline-offset-4">
            {task.libelle}
          </h1>
          <div className="prose prose-base mt-5 max-w-[50ch] text-justify font-normal">
            {parse(task.consigne)}
          </div>
          <Button
            onClick={() => {
              if (!isPlaying) {
                play();
                setIsPlaying(true);
              }
            }}
            disabled={isSoundLoading || isPlaying}
          >
            Ecoutez la consigne <Play className="ml-2 h-5 w-5 text-white" />
          </Button>
        </div>

        <div className="md:w-[50%]">
          {task.images?.map((img, i) => (
            <ImageZoomInOut
              imageUrl={
                img.trim().length > 0 ? `${BUCKET_BASE_URL}/${img}` : logo
              }
              placeholder={img.trim().length > 0 ? 'empty' : 'blur'}
              key={i}
            />
          ))}
        </div>
      </div>

      {canShow(current) && (
        <VoiceVisualizer
          width={'350px'}
          height={'170px'}
          barWidth={3}
          gap={1.5}
          defaultMicrophoneIconColor="#118ff5"
          defaultAudioWaveIconColor="#118ff5"
          mainBarColor="#118ff5"
          secondaryBarColor="#77c2fe"
          controls={recorderControls}
          ref={audioRef}
        />
      )}
      {prevFileOne && prevFileTwo ? (
        <div className="my-5 w-full max-w-md space-y-4 rounded-lg border p-4 shadow-sm">
          <h1 className="text-lg font-semibold">Vos enregistrements</h1>
          <div className="flex flex-col gap-4">
            <h2>Section A</h2>
            <audio
              className="w-full"
              src={
                isSafari
                  ? webkitURL.createObjectURL(
                      new Blob([prevFileOne], { type: prevFileOne.type }),
                    )
                  : URL.createObjectURL(
                      new Blob([prevFileOne], { type: prevFileOne.type }),
                    )
              }
              controls
            />
            <h2>Section B</h2>
            <audio
              className="w-full"
              src={
                isSafari
                  ? webkitURL.createObjectURL(
                      new Blob([prevFileTwo], { type: prevFileTwo.type }),
                    )
                  : URL.createObjectURL(
                      new Blob([prevFileTwo], { type: prevFileTwo.type }),
                    )
              }
              controls
            />
          </div>
          <div className="flex items-center justify-center gap-4">
            <Button
              variant={'destructive'}
              onClick={() => {
                prevSetFileOne(null);
                prevSetFileTwo(null);
              }}
            >
              Annuler
            </Button>
            <Button
              onClick={() => {
                prevSetFileOne(null);
                prevSetFileTwo(null);
                setFileOne(prevFileOne);
                setFileTwo(prevFileTwo);
                toast.success('Enregistrements restaurés avec succès !');
                setTriggerEnd(true);
              }}
            >
              Accepter
            </Button>
          </div>
        </div>
      ) : null}
      <div id="scroll"></div>
    </div>
  );
});

TaskEO.displayName = 'TASKEO';
export default TaskEO;
