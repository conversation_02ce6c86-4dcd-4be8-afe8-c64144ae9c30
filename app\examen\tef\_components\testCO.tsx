'use client';
import React, {
  MutableRefObject,
  startTransition,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Button } from '@/components/ui/button';

import Image from 'next/image';
import { Loader2 } from 'lucide-react';

import { BUCKET_BASE_URL } from '@/config';
import { Resultset } from '@/types';
import { EndTextBtn } from '../../_components/button';
import { useRouter } from 'next/navigation';
import GlobalTimer from '../../tcf/_components/timer';
import parse from 'html-react-parser';
import { useTEFState } from '@/context/tef';
import { toast as sonner } from 'sonner';
import { saveTestCOUseCase } from '../actions';
import { getScoreTEF } from '@/lib/utils';
import logo from '@/public/logo4.png';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useSession } from 'next-auth/react';
import { useOfflineState } from '@/context/tcf/anonymousSate';
import logger from '@/lib/logger';
export default function TestCOTEF({ serie }: { serie: SerieTEF }) {
  const {
    current,
    next,
    prev,
    resulSet,
    setresSet,
    Restart,
    setUser,
    SetMode,
    setSerie,
    setCurrent,
  } = useTEFState();
  const { data: session } = useSession();
  const { setDetailSet } = useOfflineState();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const stopBtnRef = useRef<HTMLButtonElement>(null);
  const TimeName = `CO_TIME_${serie.libelle}_${session?.user._id}`;
  const ResName = `CO_RES_${serie.libelle}_${session?.user._id}`;
  useEffect(() => {
    SetMode('CO');
    setUser(session?.user ?? null);
    setSerie(serie);
    const PrevRes = JSON.parse(
      localStorage.getItem(ResName) ?? '[]',
    ) as Resultset[];
    if (PrevRes.length > 0) {
      setresSet(PrevRes);
    }
  }, [session, ResName]);

  useEffect(() => {
    window.history.pushState(null, '', window.location.href);
    const listener = (e: PopStateEvent) => {
      window.history.pushState(null, '', window.location.href);
      stopBtnRef.current?.click();
    };
    window.addEventListener('popstate', listener);
    return () => {
      window.removeEventListener('popstate', listener);
    };
  }, []);
  const clearLocalStorage = async () => {
    window.localStorage.removeItem(TimeName);
    window.localStorage.removeItem(ResName);
  };
  const [starting, setstarting] = useState<boolean>(true);
  const questions = serie.coQuestions.questions
    .sort((q1, q2) => q1.numero - q2.numero)
    .filter((q) => q.libelles.length > 0 && q.consignes.length > 0);
  const question = questions?.[current];
  const libelles = question.libelles.filter((libelle) => libelle !== null);
  const consignes = question.consignes.filter((consigne) => consigne);
  const router = useRouter();
  const time = new Date();
  time.setSeconds(time.getSeconds() + serie.coQuestions.duree * 60);
  const StopTo = (cb: () => void) => {
    audioRef?.current?.pause();
    cb();
  };
  const onExpire = async () => {
    startTransition(() => {
      StopTo(() => {});
    });
    setstarting(false);
    if (resulSet.length > 0) {
      setIsSubmitting(true);
      if (!session?.user) {
        const data: any = {
          _id: 'random_id',
          serie: serie,
          payload: JSON.stringify(resulSet, null, 0),
          resultat: getScoreTEF(serie, resulSet, 'CO') ?? 0 ?? 0,
          createdAt: new Date().toISOString(),
          user: null,
        };
        setDetailSet(data);
        clearLocalStorage();
        Restart();
        router.push(`/resultat/TEF/CO`);
      } else {
        sonner.promise(
          saveTestCOUseCase({
            resulset: resulSet,
            serieId: serie._id,
            resultat: getScoreTEF(serie, resulSet, 'CO') ?? 0,
          }),
          {
            loading: 'Correction du test...',
            success: () => {
              clearLocalStorage();
              Restart();
              return 'Votre test a été enregistré avec succès';
            },
            error: (error) => {
              logger.error('Error saving TEF CO test', error);

              setIsSubmitting(false);
              return error.message === 'Failed to fetch'
                ? 'Verifier votre connexion'
                : error.message;
            },
            closeButton: true,
            duration: 1000 * 20,
          },
        );
      }
    } else {
      Restart();
      await clearLocalStorage();
      router.push(`/examen/tef/${serie.libelle}`);
      sonner.warning("Vous n'avez répondu à aucune question", {
        duration: 1000 * 10,
        closeButton: true,
        description: 'Ce test sera annulé',
      });
    }
  };

  return (
    <div className="relative flex w-full flex-col justify-center gap-5">
      <div className="fixed inset-x-0 top-0 z-40 flex max-h-fit w-full flex-col items-center justify-around border-b bg-white p-2 md:flex-row">
        <div className="flex items-center gap-px">
          <GlobalTimer
            mode="CO"
            serieLib={serie.libelle}
            recordTime={true}
            expiryTimestamp={time}
            onExpire={() => onExpire()}
            starting={starting}
            totalduration={serie.coQuestions.duree * 60}
          />
          <EndTextBtn onClick={() => onExpire()}>
            <button
              title="stop"
              ref={stopBtnRef}
              disabled={isSubmitting}
              className="flex aspect-video items-center justify-center rounded-sm bg-red-500 p-1.5 md:hidden md:p-3"
            >
              <p className="font-semibold text-white">Fin</p>
            </button>
          </EndTextBtn>
        </div>
        <div className="p-2 tracking-wide md:tracking-wider">
          <p className="text-center text-xs font-semibold md:text-base">
            TEF: Objectif-Canada || Série {serie.libelle} || Compréhension
            orale.
          </p>
        </div>
        <EndTextBtn onClick={() => onExpire()}>
          <button
            disabled={isSubmitting}
            title="stop"
            className="hidden aspect-video items-center justify-center rounded-sm bg-red-500 p-3 md:flex"
          >
            <p className="font-semibold text-white">Fin</p>
          </button>
        </EndTextBtn>
      </div>
      <div className="mt-20 flex gap-5">
        <div className="flex-1">
          <LibelleComponent
            libelles={libelles}
            questions={questions}
            audioRef={audioRef}
          />
          <SuggestionConsigne numero={question.numero} consignes={consignes} />
          <div className="mx-auto flex w-full items-center justify-end gap-4 md:w-full md:max-w-2xl">
            <Button
              variant={'outline'}
              className="my-2 rounded-l-full rounded-r-full"
              disabled={current == 0}
              onClick={() => {
                prev();
              }}
            >
              Précédent
            </Button>
            {current + 1 == questions.length ? (
              <Button
                className="my-2 rounded-l-full rounded-r-full"
                variant={'destructive'}
                onClick={() => onExpire()}
              >
                Fin
              </Button>
            ) : (
              <Button
                className="my-2 rounded-l-full rounded-r-full"
                onClick={() => {
                  next();
                }}
              >
                Suivant
              </Button>
            )}
          </div>
        </div>
        <div className="hidden h-fit md:block">
          <div className="row13 grid grid-cols-3 gap-1.5">
            {questions?.map((q, i) => (
              <Button
                key={`${q.numero}-${i}`}
                onClick={() => {
                  setCurrent(i);
                }}
                className={`w-full rounded-none ${
                  resulSet.filter((r) => r.questionId == q.numero).length ==
                  questions[i].consignes.length
                    ? ''
                    : 'bg-emerald-300'
                }`}
              >
                {q.numero}
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

interface LibelleComponentProps {
  libelles: (Libelle | null)[];
  questions: Question[];
  audioRef: MutableRefObject<HTMLAudioElement | null>;
}
const LibelleComponent = ({
  libelles,
  questions,
  audioRef,
}: LibelleComponentProps) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageKey, setImageKey] = useState(0);
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());

  const libelleImg = libelles.find(
    (libelle) => libelle?.typeLibelle == 'image',
  );
  const libelleAudio = libelles.find(
    (libelle) => libelle?.typeLibelle == 'audio',
  );

  const { next, current } = useTEFState();

  // Effet pour gérer le changement d'image
  useEffect(() => {
    const hasRealImage = libelleImg && libelleImg.libelle;

    if (!hasRealImage) {
      // Si c'est le logo, pas de chargement nécessaire et pas de changement de clé
      setImageLoading(false);
      return;
    }

    const currentImageSrc = `${BUCKET_BASE_URL}/${libelleImg.libelle}`;

    // Si l'image a déjà été chargée, pas besoin d'afficher le loader
    if (loadedImages.has(currentImageSrc)) {
      setImageLoading(false);
    } else {
      setImageLoading(true);
    }

    // Ne changer la clé que pour les vraies images
    setImageKey((prev) => prev + 1);
  }, [current, libelleImg?.libelle, loadedImages]);

  return (
    <div className="relative space-y-3">
      <div className="relative mx-auto aspect-square max-h-[50vh] w-full rounded-sm border md:max-h-[350px] md:w-full md:max-w-2xl">
        {/* Indicateur de chargement */}
        {imageLoading && (
          <div className="absolute inset-0 z-10 flex items-center justify-center bg-gray-100/80 backdrop-blur-sm">
            <div className="flex flex-col items-center gap-2">
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
              <p className="text-sm text-gray-600">Chargement de l'image...</p>
            </div>
          </div>
        )}
        <Image
          key={imageKey}
          alt="image-question"
          src={libelleImg ? `${BUCKET_BASE_URL}/${libelleImg?.libelle}` : logo}
          fill
          sizes="(min-width: 1024px) 1024px, 100vw"
          priority
          className={`object-contain transition-opacity duration-300 ${
            imageLoading ? 'opacity-0' : 'opacity-100'
          }`}
          onLoad={() => {
            const hasRealImage = libelleImg && libelleImg.libelle;
            if (hasRealImage) {
              const currentImageSrc = `${BUCKET_BASE_URL}/${libelleImg.libelle}`;
              setLoadedImages((prev) => new Set(prev).add(currentImageSrc));
            }
            setImageLoading(false);
          }}
          onError={() => {
            const hasRealImage = libelleImg && libelleImg.libelle;
            if (hasRealImage) {
              const currentImageSrc = `${BUCKET_BASE_URL}/${libelleImg.libelle}`;
              setLoadedImages((prev) => new Set(prev).add(currentImageSrc));
            }
            setImageLoading(false);
          }}
        />
      </div>

      <div className="mx-auto mb-3 flex w-full overflow-hidden rounded-lg md:mt-0 md:w-full md:max-w-2xl">
        <audio
          className="w-full"
          onEnded={() => {
            startTransition(() => {
              next();
            });
          }}
          autoPlay
          controls
          ref={audioRef}
          preload="metadata"
          src={`${BUCKET_BASE_URL}/${libelleAudio?.libelle}`}
        ></audio>
      </div>
    </div>
  );
};

interface SuggestionConsigneProps {
  consignes: Consigne[];
  numero: number;
}
const SuggestionConsigne = ({ consignes, numero }: SuggestionConsigneProps) => {
  const { Addset, resulSet } = useTEFState();

  const LETTERS = ['A', 'B', 'C', 'D'] as const;
  return (
    <div className="">
      {consignes.length > 1 ? (
        <div className="mx-auto grid w-full gap-4 md:w-full md:max-w-2xl">
          {consignes?.map((consigne, index) => (
            // <div
            //   key={consigne._id}
            //   className="flex h-fit flex-col gap-3 rounded-sm border p-3 leading-4"
            // >
            //   <div className="flex items-center">
            //     <span className="mx-2 rounded-sm bg-blue-500 px-4 py-2 text-white">
            //       {numero + index}
            //     </span>
            //     <span className="prose">{parse(consigne.consigne)}</span>
            //   </div>
            //   <select
            //     title="response"
            //     onChange={(e) => {
            //       const resId = e.target.value as string;

            //       Addset({
            //         questionId: numero,
            //         consigneId: consigne._id,
            //         resId: resId,
            //       });
            //     }}
            //     value={
            //       resulSet.find(
            //         (res) =>
            //           res.consigneId === consigne._id &&
            //           res.questionId == numero,
            //       )?.resId || ""
            //     }
            //     className="mx-2 mt-4 max-w-[30ch] rounded-sm border border-zinc-700 p-1 md:my-0"
            //   >
            //     <option value={""} disabled hidden>
            //       ____
            //     </option>
            //     {consigne.suggestions.map((suggestion) => (
            //       <option key={suggestion._id} value={suggestion._id}>
            //         {suggestion.text}
            //       </option>
            //     ))}
            //   </select>
            // </div>
            <div key={consigne._id} className="inline rounded-sm border p-4">
              {/* <div className="flex items-center"> */}
              <span className="mx-2 rounded-sm bg-blue-500 px-4 py-2 text-white">
                {numero + index}
              </span>
              <span className="prose">{parse(consigne.consigne)}</span>
              <span className="mx-1 inline-block bg-white align-middle">
                <Select
                  onValueChange={(val) => {
                    Addset({
                      questionId: numero,
                      consigneId: consigne._id,
                      resId: val,
                    });
                  }}
                  value={
                    resulSet.find(
                      (res) =>
                        res.consigneId === consigne._id &&
                        res.questionId === numero,
                    )?.resId || ''
                  }
                >
                  <SelectTrigger className="h-fit w-auto border border-zinc-700 p-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent position="item-aligned">
                    <SelectItem disabled hidden value="">
                      ________
                    </SelectItem>
                    {consigne.suggestions.map((suggestion) => (
                      <SelectItem key={suggestion._id} value={suggestion._id}>
                        {suggestion.text}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </span>
            </div>
          ))}
        </div>
      ) : (
        <>
          <div className="mx-auto flex w-full flex-col gap-3 overflow-hidden rounded-lg border-2 border-t-0 border-blue-400 md:mt-0 md:w-full md:max-w-2xl">
            <div className="flex w-full items-center gap-5 bg-blue-500 p-1">
              <span className="flex h-10 w-16 cursor-pointer items-center justify-center rounded-md bg-white p-4 text-base font-bold md:text-xl">
                {numero}
              </span>
              <h1 className="text-sm font-medium tracking-widest text-white md:text-base">
                {parse(consignes[0].consigne)}
              </h1>
            </div>
            <div className="flex flex-col items-start justify-center gap-2 rounded-sm bg-white p-1">
              {consignes[0].suggestions
                .filter((reponse) => reponse != null)
                .map((reponse, i) => (
                  <div
                    key={reponse?._id}
                    className="group flex cursor-pointer items-center gap-5 p-1"
                    onClick={() => {
                      Addset({
                        questionId: numero,
                        resId: reponse?._id,
                      });
                    }}
                  >
                    <span
                      className={`group-hover:bg-blue-500 ${
                        resulSet.find((q) => q.questionId == numero)?.resId ===
                        reponse?._id
                          ? 'bg-blue-500'
                          : 'bg-gray-400'
                      } flex h-6 w-6 items-center justify-center rounded-full p-4 text-sm font-semibold text-white`}
                    >
                      {LETTERS.at(i)}
                    </span>
                    <h1
                      className={`text-sm font-medium tracking-widest md:text-base ${
                        resulSet.find((q) => q.questionId == numero)?.resId ===
                        reponse?._id
                          ? 'font-semibold text-blue-500'
                          : ''
                      }`}
                    >
                      {reponse?.text}
                    </h1>
                  </div>
                ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
};
