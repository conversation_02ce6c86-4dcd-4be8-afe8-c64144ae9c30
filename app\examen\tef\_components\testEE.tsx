'use client';
import React, { useEffect, useState } from 'react';

import { GLOBAL_EE_TEST_TIME } from '@/config';
import { EndTextBtn } from '../../_components/button';
import { useRouter } from 'next/navigation';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Task from './task';
import convert from '@/lib/helpers';
import GlobalTimer from '../../tcf/_components/timer';
import { useEEStateTEF } from '@/context/tef';
import { toast as sonner } from 'sonner';
import { saveTestEEUseCase } from '../actions';
import wordCount from '@/lib/word-count';
import useCredits from '@/hooks/use-credits';
import { useSession } from 'next-auth/react';
export default function TestEETEF({ serie }: { serie: SerieTEF }) {
  const {
    textOne,
    textTwo,
    resetEE,
    setTextOne,
    setTextTwo,
    setLocalVariables,
  } = useEEStateTEF();

  const { data: session, update } = useSession();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [starting, setstarting] = useState<boolean>(true);
  const [selectedTab, setSelectedTab] = useState<string>('task1');
  const [LOCAL_TEXT_1, setLOCAL_TEXT_1] = useState<string>();
  const [LOCAL_TEXT_2, setLOCAL_TEXT_2] = useState<string>();
  const [TimeName, setTimeName] = useState<string>();

  const taskOne = serie.eeQuestions[0].sections.find((t) => t.numero == 41)!;
  const taskTwo = serie.eeQuestions[0].sections.find((t) => t.numero == 42)!;

  useEffect(() => {
    if (session && serie) {
      setLOCAL_TEXT_1(`EE_RES_${serie.libelle}_TASK_1_${session.user._id}`);
      setLOCAL_TEXT_2(`EE_RES_${serie.libelle}_TASK_2_${session.user._id}`);
      setTimeName(`EE_TIME_${serie.libelle}_${session.user._id}`);
    }
  }, [session, serie]);

  const clearLocalStorage = async () => {
    if (TimeName) localStorage.removeItem(TimeName);
    if (LOCAL_TEXT_1) localStorage.removeItem(LOCAL_TEXT_1);
    if (LOCAL_TEXT_2) localStorage.removeItem(LOCAL_TEXT_2);
  };
  useEffect(() => {
    if (!LOCAL_TEXT_1 || !LOCAL_TEXT_2) return;
    setLocalVariables({ LOCAL_TEXT_1, LOCAL_TEXT_2 });
    const prevTextOne = JSON.parse(localStorage.getItem(LOCAL_TEXT_1) ?? '{}')
      .text as string;
    const prevTextTwo = JSON.parse(localStorage.getItem(LOCAL_TEXT_2) ?? '{}')
      .text as string;
    if (prevTextOne) setTextOne(prevTextOne);
    if (prevTextTwo) setTextTwo(prevTextTwo);
  }, [LOCAL_TEXT_1, LOCAL_TEXT_2]);
  const { checkCredit } = useCredits();
  const router = useRouter();
  const time = new Date();
  time.setSeconds(time.getSeconds() + GLOBAL_EE_TEST_TIME);

  const onExpire = async () => {
    setstarting(false);
    if (
      convert(textOne).trim().length > 0 ||
      convert(textTwo).trim().length > 0
    ) {
      setIsSubmitting(true);
      if (!(await checkCredit('TEF', 'EE'))) {
        setIsSubmitting(false);
        return;
      }

      const payloadValue = {
        textOne,
        textTwo,
      };
      sonner.promise(
        saveTestEEUseCase({
          payload: payloadValue,
          serie: serie._id,
        }),
        {
          loading: 'Envoie du test en cours...',
          success: () => {
            update({
              remains: {
                remainTEF: {
                  balance_ee: session?.user.remains?.remainTEF?.balance_ee! - 1,
                  balance_eo: session?.user.remains?.remainTEF?.balance_eo,
                  remain_day: session?.user.remains?.remainTEF?.remain_day,
                },
                remainTCF: session?.user.remains?.remainTCF,
              },
            });
            clearLocalStorage();
            resetEE();
            router.back();
            return "Vos productions écrites ont bien été soumises. Vous serrez informé(e) lorsqu'un résultat sera disponible";
          },
          error: (error) => {
            setIsSubmitting(false);
            return error.message === 'Failed to fetch'
              ? 'Verifier votre connexion'
              : error.message;
          },
          closeButton: true,
          duration: 1000 * 20,
        },
      );
    } else {
      resetEE();
      clearLocalStorage();
      router.push(`/examen/tef/${serie.libelle}`);
      sonner.warning('Veuillez écrire vos 2 productions avant de soumettre', {
        description: 'Vous pourrez terminer votre test plus tard',
        duration: 1000 * 20,
        closeButton: true,
      });
    }
  };
  const handleTab2Click = (e: React.MouseEvent) => {
    if (wordCount(convert(textOne || '')) < taskOne.minWord) {
      sonner.error(
        `Vous devez avoir au moins ${taskOne.minWord} mots pour passer à la section B`,
        {
          duration: 1000 * 20,
          closeButton: true,
        },
      );
      setSelectedTab('task1');
    } else {
      setSelectedTab('task2');
    }
  };
  return (
    <div className="relative flex w-full flex-col gap-5">
      <div className="fixed inset-x-0 top-0 z-40 flex w-full flex-col items-center justify-around border-b bg-white p-2 md:flex-row">
        <div className="flex items-center gap-px">
          <GlobalTimer
            mode="EE"
            serieLib={serie.libelle}
            recordTime={true}
            expiryTimestamp={time}
            onExpire={() => onExpire()}
            starting={starting}
            totalduration={GLOBAL_EE_TEST_TIME}
          />
          <EndTextBtn onClick={() => onExpire()}>
            <button
              title="stop"
              disabled={isSubmitting}
              className="flex aspect-video items-center justify-center rounded-sm bg-red-500 p-1.5 md:hidden md:p-3"
            >
              <p className="font-semibold text-white">Fin</p>
            </button>
          </EndTextBtn>
        </div>
        <div className="p-2 tracking-wide md:tracking-wider">
          <p className="text-center text-xs font-semibold md:text-base">
            TEF: Objectif-Canada || Série {serie.libelle} || Production écrite.
          </p>
        </div>
        <EndTextBtn onClick={() => onExpire()}>
          <button
            disabled={isSubmitting}
            title="stop"
            className="hidden aspect-video items-center justify-center rounded-sm bg-red-500 p-3 md:flex"
          >
            <p className="font-semibold text-white">Fin</p>
          </button>
        </EndTextBtn>
        <p></p>
      </div>

      <div className="mt-20 w-full md:mt-14">
        <Tabs value={selectedTab} defaultValue="task1" className="h-fit w-full">
          <TabsList className="!relative !gap-3">
            <TabsTrigger
              value="task1"
              onClick={() => setSelectedTab('task1')}
              className={`font-normal tracking-wider data-[state=active]:!bg-blue-500 data-[state=active]:!font-semibold data-[state=active]:!text-white`}
            >
              Section A
            </TabsTrigger>
            <TabsTrigger
              value="task2"
              onClick={handleTab2Click}
              className={`font-normal ${wordCount(convert(textOne || '')) < taskOne.minWord ? 'cursor-not-allowed' : ''} tracking-wider data-[state=active]:!bg-blue-500 data-[state=active]:!font-semibold data-[state=active]:!text-white`}
            >
              Section B
            </TabsTrigger>
          </TabsList>
          <TabsContent value="task1">
            <Task num={1} text={textOne} task={taskOne} />
          </TabsContent>
          <TabsContent value="task2">
            <Task num={2} text={textTwo} task={taskTwo} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
