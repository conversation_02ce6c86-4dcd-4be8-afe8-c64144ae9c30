interface Suggestion {
  text: string;
  isCorrect: boolean;
  _id: string;
}

interface Consigne {
  consigne: string;
  suggestions: Suggestion[];
  _id: string;
}

interface Libelle {
  libelle: string;
  typeLibelle: string;
  _id: string;
}

interface Categorie {
  libelle: string;
  point: number;
  _id: string;
}

interface Discipline {
  libelle: string;
  duree: number;
  _id: string;
}

interface Question {
  _id: string;
  numero: number;
  libelles: (Libelle | null)[];
  consignes: Consigne[];
  categorie: Categorie;
  duree: number;
  discipline: Discipline;
  __v: number;
}

interface CoQuestions {
  total: number;
  duree: number;
  questions: Question[];
}

interface CeQuestions {
  duree?: number;
  total: number;
  questions: Question[];
}

interface Section {
  numero: number;
  libelle: string;
  consigne: string;
  images: string[];
  minWord: number;
  maxWord: number;
  typeProduction: string;
  correction?: string;
  _id: string;
}

interface EeQuestions {
  _id: string;
  sections: Section[];
}

interface SectionEO {
  numero: number;
  libelle: string;
  consigne: string;
  correction?: string;
  images: string[];
  duree: number;
  _id: string;
}

interface EoQuestions {
  _id: string;
  sections: SectionEO[];
}

interface EEPayloadTEF {
  textOne: string;
  textTwo: string;
}
interface EOPayloadTEF {
  taskUrl1: string | null;
  taskUrl2: string | null;
}

interface SerieTEF {
  _id: string;
  libelle: string;
  coQuestions: CoQuestions;
  ceQuestions: CeQuestions;
  eeQuestions: EeQuestions[];
  eoQuestions: EoQuestions[];
}

interface NQuestion {
  numero_question: number;
  numero_consigne: number;
  consigne: string;
  suggestions: Suggestion[];
  libelles: (Libelle | null)[];
  categorie: Categorie;
  duree: number;
  discipline: Discipline;
}

interface Questionnaire {
  questions: NQuestion[];
}
