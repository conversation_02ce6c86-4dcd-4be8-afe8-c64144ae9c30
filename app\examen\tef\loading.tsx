import { Skeleton } from '@/components/ui/skeleton';
import React from 'react';

function loading() {
  return (
    <section className="mt-4 flex h-fit flex-col items-center justify-start gap-10 overflow-clip md:container md:p-0">
      <h1 className="text-xl font-bold text-black">Débutez la simulation !</h1>
      <p className="prose">
        Vous êtes proche de votre objectif ! Choisissez une série, ensuite
        sélectionnez la discipline et commencez à vous exercer.
      </p>
      <div className="mt-3 grid w-full max-w-screen-md grid-cols-1 gap-2 lg:grid-cols-2 lg:gap-4">
        {Array.from({ length: 20 }).map((v, i) => (
          <Skeleton key={i} className="h-[80px] w-full rounded-sm border" />
        ))}
      </div>
    </section>
  );
}

export default loading;
