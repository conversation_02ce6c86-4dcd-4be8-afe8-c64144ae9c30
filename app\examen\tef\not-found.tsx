import { buttonVariants } from '@/components/ui/button';
import { Info } from 'lucide-react';
import Link from 'next/link';
import React from 'react';

function NotFound() {
  return (
    <div className="flex h-full flex-col items-center justify-center gap-1.5">
      <Info className="h-20 w-20" />
      <p className="font-medium"> {"Cette serie n'existe pas"}</p>
      <div className="flex gap-4">
        <Link href={'/'} className={buttonVariants({ variant: 'secondary' })}>
          {"Retourner à l'accueil"}
        </Link>
        <Link href={'/examen/tcf'} className={buttonVariants()}>
          {'Essayer une autre série'}
        </Link>
      </div>
    </div>
  );
}

export default NotFound;
