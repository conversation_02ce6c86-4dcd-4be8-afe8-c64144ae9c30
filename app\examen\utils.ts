// "use server"
import SERVER_API from '@/lib/axios.server';
import { handleError } from '@/lib/utils';
import { ResultatEE, ResultatResponse, Resultset } from '@/types';
import axios, { AxiosError } from 'axios';
import { Session, User } from 'next-auth';
export const countQ = (questions: Question[]) => {
  if (!questions) return 0;
  let count = 0;

  for (const question of questions) {
    count += question.consignes.filter((consigne) => consigne !== null).length;
  }

  return count;
};
export const pushResult = async (
  resultat: Resultset[],
  score: number,
  userId: string,
  token: string,
  serieId: string,
  mode: 'CE' | 'CO',
): Promise<ResultatResponse | undefined> => {
  const data = {
    serie: serieId,
    user: userId,
    resultat: score,
    payload: JSON.stringify(resultat, null, 0),
  };

  let urlpath = '';
  if (mode == 'CE') urlpath = 'TestCE/created';
  if (mode == 'CO') urlpath = 'TestCO/created';

  try {
    const { data: res } = await axios.post<ResultatResponse>(
      `${process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || ''}/api/${urlpath}`,
      data,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      },
    );
    return res;
  } catch (e) {
    handleError(e);
    throw new Error("impossible d'enregister votre resultat", { cause: e });
  }
};

export const pushEEResult = async (data: any, token: string) => {
  try {
    await axios.post(
      `${process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || ''}/api/eeTest/created`,
      data,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      },
    );
  } catch (e) {
    if (e instanceof AxiosError) {
      // console.log(e);
      if (e.code === 'ERR_BAD_REQUEST')
        if (e.response?.data.message === 'this user is also connect') {
          throw new Error(
            'Vous ne pouvez pas être connecter sur plusieurs appareils',
          );
        } else {
          throw new Error('Format incorrect');
        }
      if (
        e.code === 'ERR_NETWORK' ||
        e.code === 'ETIMEDOUT' ||
        e.code === 'ENOTFOUND' ||
        e.code === 'ECONNRESET'
      )
        throw new Error('Verifier votre connexion internet');
    }
    throw new Error("impossible d'enregister votre resultat");
  }
};

export const pushEOResult = async (
  data: {
    user: string;
    serie: string;
    payload: string;
  },
  token: string,
) => {
  try {
    const { data: newRes } = await axios.post<ResultatEE>(
      `${process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || ''}/api/eoTest/created`,
      data,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
          Authorization: `Bearer ${token}`,
        },
      },
    );
    return newRes;
  } catch (e) {
    if (e instanceof AxiosError) {
      // console.log(e);
      if (e.code === 'ERR_BAD_REQUEST')
        if (e.response?.data.message === 'this user is also connect') {
          throw new Error(
            'Vous ne pouvez pas être connecter sur plusieurs appareils',
          );
        } else {
          throw new Error('Format incorrect');
        }
      if (
        e.code === 'ERR_NETWORK' ||
        e.code === 'ETIMEDOUT' ||
        e.code === 'ENOTFOUND' ||
        e.code === 'ECONNRESET'
      )
        throw new Error('Verifier votre connexion internet');
    }
    throw new Error("impossible d'enregister votre resultat");
  }
};

export const pushResultTEF = async (
  resultat: Resultset[],
  userId: string,
  serieId: string,
  score: number,
  mode: 'CE' | 'CO',
): Promise<ResultatResponse | undefined> => {
  const payload = JSON.stringify(resultat, null, 0);

  const data = {
    serie: serieId,
    user: userId,
    resultat: score,
    payload: payload,
  };
  // console.log(data);

  let urlpath = '';
  if (mode == 'CE') urlpath = 'TestCE/created';
  if (mode == 'CO') urlpath = 'TestCO/created';

  try {
    const { data: res } = await SERVER_API.post<ResultatResponse>(
      `/api/tef/${urlpath}`,
      data,
    );
    return res;
  } catch (e) {
    handleError(e);
    throw new Error("impossible d'enregister votre resultat", { cause: e });
  }
};

export async function convertToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = () => {
      if (reader.result) {
        resolve(reader.result.toString());
      } else {
        reject(new Error('Échec de la conversion en Base64'));
      }
    };

    reader.onerror = () => {
      reject(new Error('Erreur lors de la lecture du fichier'));
    };

    reader.readAsDataURL(file);
  });
}

export async function convertFromBase64(
  base64: string,
  fileName: string,
): Promise<File> {
  return new Promise((resolve, reject) => {
    try {
      const arr = base64.split(',');
      const mime = arr[0].match(/:(.*?);/)?.[1] || '';
      const bstr = atob(arr[1]);
      let n = bstr.length;
      const u8arr = new Uint8Array(n);

      while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
      }

      const file = new File([u8arr], fileName, { type: mime });
      resolve(file);
    } catch (error) {
      reject(new Error('Échec de la conversion depuis Base64'));
    }
  });
}

export const getUser = async (session: Session) => {
  const { data } = await axios.get<User>(
    `${process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || ''}/api/user/users/user-info`,
    {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${session.user.accessToken}`,
      },
    },
  );
  return data;
};
