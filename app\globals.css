@tailwind base;
@tailwind components;
@tailwind utilities;

.swiper-slide {
  transition: border 0.3s ease-in !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
.swiper-slide-active > div {
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;
  }
}
@font-face {
  font-family: 'Poppins';
  src:
    url('../assets/fonts/Poppins/Poppins-Medium.ttf'),
    url('../assets/fonts/Poppins/Poppins-Bold.ttf') format('truetype'),
    url('../assets/fonts/Poppins/Poppins-Regular.ttf') format('truetype'),
    url('../assets/fonts/Poppins/Poppins-SemiBold.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Poppins';
  src:
    url('../assets/fonts/Poppins/Poppins-Medium.ttf'),
    url('../assets/fonts/Poppins/Poppins-Bold.ttf') format('truetype'),
    url('../assets/fonts/Poppins/Poppins-Regular.ttf') format('truetype'),
    url('../assets/fonts/Poppins/Poppins-SemiBold.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  em {
  @apply italic;
}
}

*,
*::before,
*::after {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

body {
  /* font-family: 'Poppins', sans-serif; */
  font-size: 1rem;
  line-height: 1.5;
}

* {
  margin: 0;
  padding: 0;
  font: inherit;
  /* outline: orange 1px solid; */
}

.row13 {
  grid-template-rows: repeat(13, minmax(0, 1fr));
}
.voice-visualizer__audio-info-container {
  height: fit-content !important;
}
/* .voice-visualizer__btn-center img {
display: none !important;
} */

@media (min-width: 1024px) {
  .isolation {
    position: relative;
  }
  .isolation::before {
    content: open-quote;
    font-family: 'Poppins', sans-serif;
    z-index: -10;
    font-weight: 900;
    font-size: 10rem;
    color: #13a2fa;
    position: absolute;
    top: -4.5rem;
    left: -3.25rem;
  }
  .isolation::after {
    content: close-quote;
    z-index: -10;
    font-family: 'Poppins', sans-serif;
    font-weight: 900;
    font-size: 10rem;
    color: #13a2fa;
    position: absolute;
    bottom: -3.5rem;
    right: -2rem;
  }
}

.bgc {
  background-image: url(/logo2.jpg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  isolation: isolate;
  position: relative;
}

.bga {
  background-image: url(/logo2.jpg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  isolation: isolate;
  position: relative;
}

.bgc::after {
  position: absolute;
  inset: 0;
  content: '';
  background: rgba(255, 255, 255, 0.8);
  z-index: -1;
}

.swiper-pagination-bullets {
  bottom: -40px !important;
}

.swiper-pagination-bullet {
  width: 15px !important;
  height: 15px !important;
  margin: 0 5px !important; /* added */
}

@media ((max-width: 1080px)) {
  .swiper {
    overflow-y: visible !important;
  }
}

/* h1,h2,h3,h4 {
  font-family: 'Poppins';
} */

.radial-gradient {
  background:
    radial-gradient(
      circle at 50% 0%,
      rgba(32, 91, 200, 0.158) 0%,
      transparent 60%
    ),
    rgb(0, 16, 40);
}

.linear-mask {
  mask-image: linear-gradient(
    -75deg,
    white calc(var(--x) + 20%),
    transparent calc(var(--x) + 30%),
    white calc(var(--x) + 100%)
  );
  -webkit-mask-image: linear-gradient(
    -75deg,
    white calc(var(--x) + 20%),
    transparent calc(var(--x) + 30%),
    white calc(var(--x) + 100%)
  );
}

.linear-overlay {
  background-image: linear-gradient(
    -75deg,
    rgba(15, 170, 255, 0.5) calc(var(--x) + 20%),
    rgb(0, 102, 255) calc(var(--x) + 25%),
    rgba(0, 115, 255, 0.406) calc(var(--x) + 100%)
  );
  mask:
    linear-gradient(black, black) content-box,
    linear-gradient(black, black);
  -webkit-mask:
    linear-gradient(black, black) content-box,
    linear-gradient(black, black);
  mask-composite: exclude;
  -webkit-mask-composite: xor;
}

.bg-path {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' version='1.1' xmlns:xlink='http://www.w3.org/1999/xlink' xmlns:svgjs='http://svgjs.dev/svgjs' width='1440' height='565' preserveAspectRatio='none' viewBox='0 0 1440 565'%3e%3cg mask='url(%26quot%3b%23SvgjsMask1405%26quot%3b)' fill='none'%3e%3crect width='60' height='60' clip-path='url(%26quot%3b%23SvgjsClipPath1406%26quot%3b)' x='621.36' y='-0.67' fill='url(%26quot%3b%23SvgjsPattern1407%26quot%3b)' transform='rotate(189.15%2c 651.36%2c 29.33)'%3e%3c/rect%3e%3crect width='372' height='372' clip-path='url(%26quot%3b%23SvgjsClipPath1408%26quot%3b)' x='716.52' y='305.41' fill='url(%26quot%3b%23SvgjsPattern1409%26quot%3b)' transform='rotate(81.56%2c 902.52%2c 491.41)'%3e%3c/rect%3e%3crect width='366.48' height='366.48' clip-path='url(%26quot%3b%23SvgjsClipPath1410%26quot%3b)' x='1076.96' y='311.38' fill='url(%26quot%3b%23SvgjsPattern1411%26quot%3b)' transform='rotate(351.57%2c 1260.2%2c 494.62)'%3e%3c/rect%3e%3ccircle r='47.083333333333336' cx='899.05' cy='93.67' fill='rgba(200%2c 205%2c 200%2c 1)'%3e%3c/circle%3e%3ccircle r='47.083333333333336' cx='640.03' cy='167.39' fill='rgba(33%2c 152%2c 243%2c 1)'%3e%3c/circle%3e%3cpath d='M969.29 273.26L981.93 275.3 982.72 288.09 995.36 290.13 996.14 302.91 1008.78 304.95 1009.56 317.74' stroke='rgba(243%2c 60%2c 15%2c 1)' stroke-width='2.43' stroke-dasharray='2%2c 2'%3e%3c/path%3e%3crect width='144' height='144' clip-path='url(%26quot%3b%23SvgjsClipPath1412%26quot%3b)' x='123.97' y='44.28' fill='url(%26quot%3b%23SvgjsPattern1413%26quot%3b)' transform='rotate(34.77%2c 195.97%2c 116.28)'%3e%3c/rect%3e%3cpath d='M1266.37 575.01 L1363 581.3699999999999L1370.4986358395722 522.3763641604276z' fill='rgba(200%2c 205%2c 200%2c 1)'%3e%3c/path%3e%3crect width='312' height='312' clip-path='url(%26quot%3b%23SvgjsClipPath1414%26quot%3b)' x='213.47' y='216.77' fill='url(%26quot%3b%23SvgjsPattern1415%26quot%3b)' transform='rotate(125.85%2c 369.47%2c 372.77)'%3e%3c/rect%3e%3crect width='304.2' height='304.2' clip-path='url(%26quot%3b%23SvgjsClipPath1416%26quot%3b)' x='250.99' y='-6.24' fill='url(%26quot%3b%23SvgjsPattern1417%26quot%3b)' transform='rotate(190.14%2c 403.09%2c 145.86)'%3e%3c/rect%3e%3cpath d='M419.26 392.95L430.85 398.39 428.1 410.89 439.7 416.32 436.95 428.83 448.55 434.26 445.79 446.77M412.08 396.49L423.68 401.92 420.93 414.43 432.53 419.86 429.77 432.37 441.37 437.8 438.62 450.31M404.91 400.03L416.5 405.46 413.75 417.97 425.35 423.4 422.6 435.91 434.2 441.34 431.44 453.84' stroke='rgba(200%2c 205%2c 200%2c 1)' stroke-width='1.07' stroke-dasharray='2%2c 2'%3e%3c/path%3e%3cpath d='M1012.22 436.54L1003.58 427.09 1010.9 416.58 1002.25 407.14 1009.57 396.63 1000.93 387.18 1008.25 376.67M1020.21 436.01L1011.56 426.56 1018.88 416.05 1010.24 406.61 1017.56 396.1 1008.91 386.65 1016.23 376.14M1028.19 435.48L1019.54 426.03 1026.86 415.52 1018.22 406.08 1025.54 395.57 1016.89 386.12 1024.21 375.61' stroke='rgba(33%2c 152%2c 243%2c 1)' stroke-width='1' stroke-dasharray='2%2c 2'%3e%3c/path%3e%3c/g%3e%3cdefs%3e%3cmask id='SvgjsMask1405'%3e%3crect width='1440' height='565' fill='white'%3e%3c/rect%3e%3c/mask%3e%3cpattern x='0' y='0' width='60' height='6' patternUnits='userSpaceOnUse' id='SvgjsPattern1407'%3e%3crect width='60' height='3' x='0' y='0' fill='rgba(243%2c 60%2c 15%2c 1)'%3e%3c/rect%3e%3crect width='60' height='3' x='0' y='3' fill='rgba(0%2c 0%2c 0%2c 0)'%3e%3c/rect%3e%3c/pattern%3e%3cclipPath id='SvgjsClipPath1406'%3e%3ccircle r='15' cx='651.36' cy='29.33'%3e%3c/circle%3e%3c/clipPath%3e%3cpattern x='0' y='0' width='372' height='6' patternUnits='userSpaceOnUse' id='SvgjsPattern1409'%3e%3crect width='372' height='3' x='0' y='0' fill='rgba(33%2c 152%2c 243%2c 1)'%3e%3c/rect%3e%3crect width='372' height='3' x='0' y='3' fill='rgba(0%2c 0%2c 0%2c 0)'%3e%3c/rect%3e%3c/pattern%3e%3cclipPath id='SvgjsClipPath1408'%3e%3ccircle r='93' cx='902.52' cy='491.41'%3e%3c/circle%3e%3c/clipPath%3e%3cpattern x='0' y='0' width='10.18' height='10.18' patternUnits='userSpaceOnUse' id='SvgjsPattern1411'%3e%3cpath d='M5.09 1L5.09 9.18M1 5.09L9.18 5.09' stroke='rgba(200%2c 205%2c 200%2c 1)' fill='none' stroke-width='1.98'%3e%3c/path%3e%3c/pattern%3e%3cclipPath id='SvgjsClipPath1410'%3e%3ccircle r='91.62' cx='1260.2' cy='494.62'%3e%3c/circle%3e%3c/clipPath%3e%3cpattern x='0' y='0' width='6' height='6' patternUnits='userSpaceOnUse' id='SvgjsPattern1413'%3e%3cpath d='M3 1L3 5M1 3L5 3' stroke='rgba(200%2c 205%2c 200%2c 1)' fill='none' stroke-width='1.96'%3e%3c/path%3e%3c/pattern%3e%3cclipPath id='SvgjsClipPath1412'%3e%3ccircle r='36' cx='195.97' cy='116.28'%3e%3c/circle%3e%3c/clipPath%3e%3cpattern x='0' y='0' width='312' height='6' patternUnits='userSpaceOnUse' id='SvgjsPattern1415'%3e%3crect width='312' height='3' x='0' y='0' fill='rgba(33%2c 152%2c 243%2c 1)'%3e%3c/rect%3e%3crect width='312' height='3' x='0' y='3' fill='rgba(0%2c 0%2c 0%2c 0)'%3e%3c/rect%3e%3c/pattern%3e%3cclipPath id='SvgjsClipPath1414'%3e%3ccircle r='78' cx='369.47' cy='372.77'%3e%3c/circle%3e%3c/clipPath%3e%3cpattern x='0' y='0' width='11.7' height='11.7' patternUnits='userSpaceOnUse' id='SvgjsPattern1417'%3e%3cpath d='M5.85 1L5.85 10.7M1 5.85L10.7 5.85' stroke='rgba(243%2c 60%2c 15%2c 1)' fill='none' stroke-width='4.51'%3e%3c/path%3e%3c/pattern%3e%3cclipPath id='SvgjsClipPath1416'%3e%3ccircle r='76.05' cx='403.09' cy='145.86'%3e%3c/circle%3e%3c/clipPath%3e%3c/defs%3e%3c/svg%3e");
}
.swiper-container {
  width: 100% !important;
  max-width: 100% !important;
  max-height: 100vh !important;
  min-height: 0 !important;
  min-width: 0 !important;
}

.swiper-slide {
  width: auto;
  flex-shrink: 0;
  display: block;
  height: 100%;
  max-height: 100%;
  height: -webkit-max-content !important;
  height: -moz-max-content !important;
  height: max-content !important;
}

.swiper-wrapper {
  max-height: 100% !important;
  height: 100% !important;
  display: flex !important;
}

* {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  box-sizing: border-box;
}

input,
textarea,
select {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}
html {
  scroll-behavior: smooth;
  scroll-padding-top: var(--scroll-padding, 60px);
}
img {
  pointer-events: none; /* Désactiver le glisser-déposer d'images */
}

.react-sweet-progress-symbol {
  display: none !important;
}

.no-style {
  all: unset;
}

.rotate-card {
  transform: skew(-20deg);
}

p {
  margin: 0 !important;
  padding: 0 !important;
}


/* keyframe-animations.css et variables.css doivent être inclus manuellement ici */

/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*/

/* Box sizing and border defaults */
*,
::before,
::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

/* Base styles for html */
html,
:host {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
  -moz-tab-size: 4;
  -o-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
}

/* Body reset */
body {
  margin: 0;
  line-height: inherit;
}

/* HR styles */
hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

/* Abbreviations */
abbr[title] {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

/* Headings inherit font styles */
h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}

/* Link reset */
a {
  color: inherit;
  text-decoration: inherit;
}

/* Strong and bold text */
b, strong {
  font-weight: bolder;
}

/* Code elements */
code, kbd, samp, pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  font-feature-settings: normal;
  font-variation-settings: normal;
  font-size: 1em;
}

/* Small text */
small {
  font-size: 80%;
}

/* Sub/sup adjustments */
sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub { bottom: -0.25em; }
sup { top: -0.5em; }

/* Table styles */
table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

/* Form elements */
button, input, optgroup, select, textarea {
  font-family: inherit;
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button, select {
  text-transform: none;
}

button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
  appearance: button;
  -webkit-appearance: button;
  background-color: transparent;
  background-image: none;
}

/* Focus outline for Firefox */
:-moz-focusring {
  outline: auto;
}

/* Remove invalid box shadow in Firefox */
:-moz-ui-invalid {
  box-shadow: none;
}

/* Progress alignment */
progress {
  vertical-align: baseline;
}

/* Input spin button fix */
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/* Search input */
[type="search"] {
  appearance: textfield;
  -webkit-appearance: textfield;
  outline-offset: -2px;
}
::-webkit-search-decoration {
  -webkit-appearance: none;
}

/* File upload button */
::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

/* Summary display */
summary {
  display: list-item;
}

/* Reset margin/padding for block elements */
blockquote,
dl,
dd,
h1, h2, h3, h4, h5, h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol, ul, menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

dialog {
  padding: 0;
}

/* Textarea */
textarea {
  resize: vertical;
}

/* Placeholders */
input::-moz-placeholder,
textarea::-moz-placeholder {
  opacity: 1;
  color: #9ca3af;
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

/* Buttons */
button,
[role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

/* Media elements */
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
}

img,
video {
  max-width: 100%;
  height: auto;
}

/* Hidden attribute */
[hidden]:not([hidden="until-found"]) {
  display: none;
}
