/* eslint-disable react-hooks/rules-of-hooks */
import '../styles/_keyframe-animations.scss';
import '../styles/_variables.scss';
import './globals.css';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import Provider from '@/components/Provider';
import { Metadata } from 'next';
import Script from 'next/script';
import { SITE_URL } from '@/config';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import RapidLinks from '@/components/rapid-links';
export const metadata: Metadata = {
  icons: {
    icon: '/fav/favicon.ico',
  },
  metadataBase: new URL(SITE_URL),
  openGraph: {
    url: SITE_URL,
    type: 'website',
    title: 'Objectif Canada TCF',
    siteName: 'Objectif Canada TCF TEF',
    description: 'Le meilleur site de preparation au TCF et TEF Canada',
    locale: 'fr_FR',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Objectif Canada TCF',
  },
  keywords: [],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="fr" translate="no" suppressHydrationWarning>
      <head>
        <Script
          src="https://beamanalytics.b-cdn.net/beam.min.js"
          data-token={process.env.BEAM_SECRET || ''}
          async
        />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="google" content="notranslate" />
      </head>
      <body className={`overflow-x-hidden`}>
        <NuqsAdapter>
          <Provider>
            <div className="relative grid min-h-[100dvh] grid-rows-[auto_1fr_auto] items-start scroll-smooth antialiased">
              <Navbar />
              <main className="h-full max-w-[100vw]">
                <RapidLinks />
                {children}
              </main>
              <Footer />
            </div>
          </Provider>
        </NuqsAdapter>
      </body>
    </html>
  );
}
