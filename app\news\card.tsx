import { Serie } from '@/types';
import React from 'react';
import parse from 'html-react-parser';
export function NewsCardTCF({
  serie,
  type,
}: {
  serie: Serie;
  type: 'EE' | 'EO';
}) {
  const serieType = Number(serie.libelle) > 999 ? 'TEF' : 'TCF';
  return (
    <div className="flex flex-col gap-1">
      {serieType == 'TCF' ? (
        <>
          {type == 'EE'
            ? serie.eeQuestions.at(0)?.tasks.map((q) => {
                return (
                  <div key={q._id} className="flex flex-col gap-1 px-4">
                    <span className="text-lg font-bold capitalize text-blue-500 underline underline-offset-2">
                      {q.libelle} :
                    </span>{' '}
                    {parse(q.consigne)}
                  </div>
                );
              })
            : serie.eoQuestions
                .at(0)
                ?.tasks.filter((task) => task.libelle !== 'tache 1')
                .map((q) => {
                  return (
                    <div key={q._id} className="flex flex-col gap-1 px-4">
                      <span className="text-lg font-bold capitalize text-blue-500 underline underline-offset-2">
                        {q.libelle} :
                      </span>{' '}
                      {parse(q.consigne)}
                    </div>
                  );
                })}
        </>
      ) : (
        <>
          {type == 'EE'
            ? serie.eeQuestions
                .at(0)
                ?.tasks.filter(
                  (task) => task.libelle.toLowerCase() !== 'tache 3',
                )
                .map((q) => {
                  return (
                    <div key={q._id} className="flex flex-col gap-1 px-4">
                      <span className="text-lg font-bold capitalize text-blue-500 underline underline-offset-2">
                        {q.libelle} :
                      </span>{' '}
                      {parse(q.consigne)}
                    </div>
                  );
                })
            : serie.eoQuestions
                .at(0)
                ?.tasks.filter((task) => task.libelle == 'tache 2')

                .map((q) => {
                  return (
                    <div key={q._id} className="flex flex-col gap-1 px-4">
                      <span className="text-lg font-bold capitalize text-blue-500 underline underline-offset-2">
                        {q.libelle} :
                      </span>{' '}
                      {parse(q.consigne)}
                    </div>
                  );
                })}
        </>
      )}
    </div>
  );
}

export function NewsCardTEF({
  serie,
  type,
}: {
  serie: SerieTEF;
  type: 'EE' | 'EO';
}) {
  return (
    <div className="flex flex-col gap-1">
      {type == 'EE'
        ? serie?.eeQuestions
            ?.at(0)
            ?.sections.filter(
              (task) => task.libelle.toLowerCase() !== 'tache 3',
            )
            .map((q) => {
              return (
                <div key={q._id} className="flex flex-col gap-1 px-4">
                  <span className="text-lg font-bold capitalize text-blue-500 underline underline-offset-2">
                    {q.libelle} :
                  </span>{' '}
                  {parse(q.consigne)}
                </div>
              );
            })
        : serie?.eoQuestions?.at(0)?.sections.map((q) => {
            return (
              <div key={q._id} className="flex flex-col gap-1 px-4">
                <span className="text-lg font-bold capitalize text-blue-500 underline underline-offset-2">
                  {q.libelle} :
                </span>{' '}
                {parse(q.consigne)}
              </div>
            );
          })}
    </div>
  );
}
