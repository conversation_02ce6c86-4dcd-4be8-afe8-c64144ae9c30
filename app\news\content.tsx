'use client';
import Link from 'next/link';
import React from 'react';
import { NewsCardTCF, NewsCardTEF } from './card';
import ShinnyButton from '@/components/shinnyButton';
import { TracingBeam } from '@/components/ui/tracing-beam';
import { useFetchTCFNews, useFetchTEFNews, useRefetchInScroll } from './hook';

function NewsPageTCF() {
  const { data, fetchNextPage, status, isFetchingNextPage, error } =
    useFetchTCFNews();
  const { ref } = useRefetchInScroll({ onScroll: () => fetchNextPage() });

  return (
    <div className="lg: mx-auto flex w-full flex-col items-center gap-2 px-3 py-3 lg:max-w-screen-xl lg:items-center lg:px-14">
      <Header type="TCF" />
      <WarningText />
      {status === 'loading' ? (
        <div className="animate-pulse font-bold">
          Chargement des sujets <span className="text-primary">...</span>
        </div>
      ) : status === 'error' ? (
        <div>{JSON.stringify((error as any).message)}</div>
      ) : (
        <TracingBeam count={data?.pages.length}>
          <div className="flex w-full flex-col gap-2">
            {data?.pages.map((page) => {
              return (
                <div
                  key={page.currentPage}
                  className="flex w-full flex-col gap-2"
                >
                  {page?.result
                    .sort((a, b) => {
                      return parseInt(a.libelle) - parseInt(b.libelle);
                    })
                    .filter((result) => {
                      return (
                        result?.eeQuestions?.length > 0 ||
                        result?.eoQuestions?.length > 0
                      );
                    })
                    .map((result) => {
                      return (
                        <div
                          key={result._id}
                          className="flex w-full flex-col gap-2 rounded-sm border px-8 py-5"
                        >
                          <Link
                            className=""
                            href={`/examen/tcf/${result.libelle}`}
                          >
                            <ShinnyButton
                              text={`Serie ${result.libelle}`}
                              className=""
                            />
                          </Link>
                          <h2 className="w-fit rounded-sm border bg-blue-400 p-2 font-semibold text-white">
                            Expression Ecrite
                          </h2>
                          <NewsCardTCF serie={result} type="EE" />
                          <h2 className="w-fit rounded-sm border bg-blue-400 p-2 font-semibold text-white">
                            Expression Orale
                          </h2>
                          <NewsCardTCF serie={result} type="EO" />
                        </div>
                      );
                    })}
                </div>
              );
            })}
          </div>
        </TracingBeam>
      )}
      <div ref={ref}>
        {isFetchingNextPage && (
          <div className="animate-pulse font-bold">
            Chargement des sujets <span className="text-primary">...</span>
          </div>
        )}
      </div>
    </div>
  );
}

function NewsPageTEF() {
  const { data, fetchNextPage, status, isFetchingNextPage, error } =
    useFetchTEFNews();
  const { ref } = useRefetchInScroll({ onScroll: () => fetchNextPage() });

  return (
    <div className="lg: mx-auto flex w-full flex-col items-center gap-2 px-3 py-3 lg:max-w-screen-xl lg:items-center lg:px-14">
      <Header type="TEF" />
      <WarningText />
      {status === 'loading' ? (
        <div className="animate-pulse font-bold">
          Chargement des sujets <span className="text-primary">...</span>
        </div>
      ) : status === 'error' ? (
        <div>{JSON.stringify((error as any).message)}</div>
      ) : (
        <TracingBeam count={data?.pages.length}>
          <div className="flex w-full flex-col gap-2">
            {data?.pages?.map((page) => {
              return (
                <div
                  key={page.currentPage}
                  className="flex w-full flex-col gap-2"
                >
                  {page?.result
                    .sort((a, b) => {
                      return parseInt(a.libelle) - parseInt(b.libelle);
                    })
                    .filter((result) => {
                      return (
                        result?.eeQuestions?.length > 0 ||
                        result?.eoQuestions?.length > 0
                      );
                    })
                    .map((result) => {
                      return (
                        <div
                          key={result._id}
                          className="flex w-full flex-col gap-2 rounded-sm border px-8 py-5"
                        >
                          <Link
                            className=""
                            href={`/examen/tef/${result.libelle}`}
                          >
                            <ShinnyButton
                              text={`Serie ${result.libelle}`}
                              className=""
                            />
                          </Link>
                          <h2 className="w-fit rounded-sm border bg-blue-400 p-2 font-semibold text-white">
                            Expression Ecrite
                          </h2>
                          <NewsCardTEF serie={result} type="EE" />
                          <h2 className="w-fit rounded-sm border bg-blue-400 p-2 font-semibold text-white">
                            Expression Orale
                          </h2>
                          <NewsCardTEF serie={result} type="EO" />
                        </div>
                      );
                    })}
                </div>
              );
            })}
          </div>
        </TracingBeam>
      )}
      <div ref={ref}>
        {isFetchingNextPage && (
          <div className="animate-pulse font-bold">
            Chargement des sujets <span className="text-primary">...</span>
          </div>
        )}
      </div>
    </div>
  );
}

const WarningText = () => {
  return (
    <div className="w-full border border-orange-400 px-8 py-5">
      <h1 className="text-xl font-bold text-orange-800">Attention !</h1>
      <p>
        Ces sujets sont basés sur des sujets réels ! Ils ne sont pas les
        originaux.
      </p>
    </div>
  );
};

const Header = ({ type }: { type: string }) => {
  const date = new Date(Date.now());
  return (
    <div className="flex w-full flex-col justify-start gap-2 border bg-cover bg-no-repeat px-8 py-5 bg-news">
      <h1 className="font-poppins text-2xl font-bold !text-white">{`Sujets d’actualité ${type} ${date.toLocaleDateString(
        'fr-FR',
        { month: 'long' },
      )} ${date.getFullYear()}`}</h1>
      <h2 className="text-lg font-semibold !text-white">{`Découvrez Les nouveaux sujets de l’expression écrite et orale qui se répètent. Pratiquez sur ces thèmes afin d'obtenir de bonnes notes lors de votre examen du ${type}.`}</h2>
    </div>
  );
};

export { NewsPageTCF, NewsPageTEF };
