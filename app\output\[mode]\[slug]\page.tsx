import DowloadPDF from '@/components/DowloadPDF';
import SERVER_API from '@/lib/axios.server';
import {
  getExpressionNiveaau,
  getNiveau,
  getScore,
  getTestSet,
} from '@/lib/utils';
import { Resultat, ResultatEE, Resultset, ScoreC } from '@/types';
import { Metadata } from 'next';

import { notFound } from 'next/navigation';
export const metadata: Metadata = {
  title: 'TCF  | Résultat',
  description: 'visualiser vos résultat sur une série',
};
type partialMode = 'EE' | 'EO';
interface Props {
  params: {
    slug: string;
    mode: partialMode;
  };
}
const fetchDetail = async (
  id: string,
  mode: partialMode,
): Promise<ResultatEE | null> => {
  const url =
    mode == 'EE' ? `/api/eeTest/tests/${id}` : `/api/eoTest/tests/${id}`;
  try {
    const { data } = await SERVER_API.get<ResultatEE>(url);
    return data;
  } catch (error) {
    return null;
  }
};
const getLastExpression = async (
  mode: partialMode,
  serieId: string,
): Promise<ResultatEE | undefined> => {
  const url =
    mode == 'EE'
      ? `/api/eeTest/tests/user-info`
      : `/api/eoTest/tests/user-info`;
  try {
    const { data } = await SERVER_API.get<ResultatEE[]>(url);
    return data
      .filter((res) => res.serie._id === serieId && res.status === 'terminer')
      .sort((a, b) => {
        const d: any = new Date(a.createdAt);
        const c: any = new Date(b.createdAt);
        return c - d;
      })[0];
  } catch (error) {
    return undefined;
  }
};
const getLastComprehensionTest = async (
  serieLib: string,
  mode: 'CE' | 'CO',
) => {
  let urlpath = '';
  if (mode == 'CE') urlpath = 'TestCE/TestCEs';
  if (mode == 'CO') urlpath = 'TestCO/TestCOs';
  try {
    const { data } = await SERVER_API.get<Resultat[]>(
      `/api/${urlpath}/user-info`,
    );
    return data.find((test) => test.serie.libelle == serieLib);
  } catch (error) {
    return undefined;
  }
};

export default async function page({ params: { slug, mode } }: Props) {
  const detailSet = await fetchDetail(slug, mode);
  if (!detailSet) {
    notFound();
  }

  const lastE = await getLastExpression(
    mode == 'EE' ? 'EO' : 'EE',
    detailSet.serie._id,
  );
  const CE = await getLastComprehensionTest(detailSet.serie.libelle, 'CE');
  const CO = await getLastComprehensionTest(detailSet.serie.libelle, 'CO');
  let scoreCE: ScoreC | null = null;
  let scoreCO: ScoreC | null = null;
  if (CE) {
    scoreCE = getScore(
      getTestSet(CE.serie),
      JSON.parse(CE.payload) as Resultset[],
    );
  }
  if (CO) {
    scoreCO = getScore(
      getTestSet(CO.serie),
      JSON.parse(CO.payload) as Resultset[],
    );
  }

  const currentExpScore = detailSet.resultat.reduce(
    (acc, obj) => acc + obj.note,
    0,
  );
  const LastExpScore = lastE
    ? lastE.resultat.reduce((acc, obj) => acc + obj.note, 0)
    : null;
  const moy = [
    scoreCE ? getNiveau(scoreCE.CE!) : 'A1',
    scoreCO ? getNiveau(scoreCO.CE!) : 'A1',
    lastE ? getExpressionNiveaau(LastExpScore!) : 'A1',
    getExpressionNiveaau(currentExpScore),
  ].sort();
  return (
    <div className="flex min-w-[100%] items-center justify-center px-3 py-6 md:px-20 md:py-10">
      <DowloadPDF
        slug={slug}
        lastE={lastE}
        LastExpScore={LastExpScore}
        currentExpScore={currentExpScore}
        mode={mode}
        detailSet={detailSet}
        moy={moy}
        scoreCE={scoreCE}
        scoreCO={scoreCO}
      />
    </div>
  );
}
