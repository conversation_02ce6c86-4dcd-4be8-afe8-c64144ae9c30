'use client';

import Link from 'next/link';
import { useTranslation } from '@/app/i18n/client';
import { usei18nState } from '@/context/i18n';
import ShinnyButton from './shinnyButton';

export default function ActionButton() {
  const { lng } = usei18nState();
  const { t } = useTranslation(lng);

  return (
    <>
      <Link
        className="animate__animated animate__fadeIn !no-underline"
        href={`/examen/tcf`}
      >
        <ShinnyButton className="" text={t('test')} />
      </Link>
      <Link
        className="animate__animated animate__fadeIn !no-underline"
        href="/examen/tef"
      >
        <ShinnyButton className="mx-auto" text={t('test-tef')} />
      </Link>
    </>
  );
}
