'use client';
import { useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import { useSession } from 'next-auth/react';
import { toast } from './ui/use-toast';
import { usePath } from '@/hooks/use-path';
import API from '@/lib/axios';
import { User } from 'next-auth';
import { useCallback } from 'react';
import { Button } from './ui/button';
import { useRouter } from 'next/navigation';
import { hasSupscription } from '@/lib/getDayCount';

export default function RefreshSession() {
  const { data: session, update } = useSession();
  const router = useRouter();
  const informUser = useCallback(() => {
    toast({
      title: 'Mise à jour de votre session',
      description: 'Veuillez verifier votre compte.',
      action: (
        <Button onClick={() => router.push('/dashboard/tcfhistory')}>
          Mon compte
        </Button>
      ),
    });
  }, []);

  const informUserEmailForVerification = useCallback(() => {
    const { dismiss } = toast({
      title: 'Votre compte sera désactivé',
      description: 'Veuillez verifier votre addresse mail.',
      duration: 5 * 60 * 1000,
      className: 'bg-red-500 text-white',
      action: (
        <Button
          variant={'secondary'}
          className="text-sm"
          onClick={() => {
            router.push('/email-verify');
            dismiss();
          }}
        >
          Vérifiez maintenant
        </Button>
      ),
    });
  }, []);
  const path = usePath();
  const {} = useQuery(
    ['check-connexion'],
    async () => {
      const { data } = await API.get<User>(`/api/user/users/user-info`);
      return data;
    },
    {
      enabled:
        Boolean(session) &&
        !path.includes('signin') &&
        !path.includes('signup') &&
        !path.includes('email-verify') &&
        !path.includes('reset-password'),
      refetchInterval: 5 * 60 * 1000,
      refetchOnWindowFocus: 'always',
      async onSuccess(data) {
        if (
          typeof data.accountIsCheck == 'boolean' &&
          data.accountIsCheck == false &&
          hasSupscription(data)
        ) {
          informUserEmailForVerification();
        }

        if (
          session?.user?.remains?.remainTCF?.remain_day !==
          data.remains?.remainTCF?.remain_day
        ) {
          await update(data);
          informUser();
        }
        if (
          session?.user?.remains?.remainTEF?.remain_day !==
          data.remains?.remainTEF?.remain_day
        ) {
          await update(data);
          informUser();
        }
      },
      onError(err) {
        if (err instanceof AxiosError) {
          toast({
            title: 'Une erreur lors du rafraichissement de votre session',
            description: err.message,
            variant: 'destructive',
          });
        }
      },
    },
  );
  return null;
}
