import React, { ComponentProps, PropsWithChildren } from 'react';
import { But<PERSON> } from './ui/button';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CustomButtonProps {
  variant?: ComponentProps<typeof Button>['variant'];
  buttonClassName?: ComponentProps<typeof Button>['className'];
  onClick?: ComponentProps<typeof Button>['onClick'];
  disabled?: ComponentProps<typeof Button>['disabled'];
  overLayClassName?: ComponentProps<'div'>['className'];
}

function CustomButton({
  disabled,
  variant = 'default',
  buttonClassName,
  overLayClassName,
  children,
  onClick,
}: PropsWithChildren<CustomButtonProps>) {
  return (
    <Button
      disabled={disabled}
      variant={variant}
      onClick={onClick}
      className={cn('relative overflow-hidden', buttonClassName)}
      type="submit"
    >
      <div
        className={cn('absolute inset-0 bg-primary', overLayClassName, {
          'flex items-center justify-center': disabled,
          hidden: !disabled,
        })}
      >
        <Loader2 className={`h-4 w-4 animate-spin`} />
      </div>
      {children}
    </Button>
  );
}

export default CustomButton;
