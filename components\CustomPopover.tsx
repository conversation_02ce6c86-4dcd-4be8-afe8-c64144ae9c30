'use client';
import { ReactNode } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';

interface CustomPopoverProps {
  trigger: ReactNode;
  offset?: number;
  content: ReactNode;
}
export default function CustomPopover({
  trigger,
  offset = 10,
  content,
}: CustomPopoverProps) {
  return (
    <Popover>
      <PopoverTrigger asChild>{trigger}</PopoverTrigger>
      <PopoverContent className="w-fit" sideOffset={offset}>
        {content}
      </PopoverContent>
    </Popover>
  );
}
