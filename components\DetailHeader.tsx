'use client';
import Link from 'next/link';
import React from 'react';
import { buttonVariants } from './ui/button';
import { cn, getNiveau, getScore, getTestSet, isCO } from '@/lib/utils';
import { Resultat, Resultset, ScoreC } from '@/types';
import format from 'date-fns/format';
interface Props {
  detailSet: Resultat;
}
const CO = ({ score }: { score: ScoreC }) => {
  return (
    <div className="flex min-w-[110px] flex-nowrap items-center justify-center gap-1 font-bold text-gray-600 md:min-w-[110px]">
      <p className="">CO</p>
      {score.CO !== null ? (
        <span className="flex">
          <p
            className={cn('', {
              'text-red-500': score?.CO! < 450,
              'text-green-400': score?.CO! >= 450,
            })}
          >
            {score.CO}
          </p>
          /699
        </span>
      ) : (
        '---'
      )}
      <p
        className={cn('rounded-sm border bg-zinc-100 px-3 py-2', {
          'text-red-500': score?.CO! < 450,
          'text-green-400': score?.CO! >= 450,
        })}
      >
        {score.CO !== null ? getNiveau(score.CO) : ''}
      </p>
    </div>
  );
};
const CE = ({ score }: { score: ScoreC }) => {
  return (
    <div className="flex min-w-[110px] flex-nowrap items-center justify-center gap-1 font-bold text-gray-600 md:min-w-[110px]">
      <p className="">CE</p>
      {score.CE !== null ? (
        <span className="flex">
          <p
            className={cn('', {
              'text-red-500': score?.CE! < 450,
              'text-green-400': score?.CE! >= 450,
            })}
          >
            {score.CE}
          </p>
          /699
        </span>
      ) : (
        '---'
      )}
      <p
        className={cn('rounded-sm border bg-zinc-100 px-3 py-2', {
          'text-red-500': score?.CE! < 450,
          'text-green-400': score?.CE! >= 450,
        })}
      >
        {score.CE !== null ? getNiveau(score.CE) : ''}
      </p>
    </div>
  );
};

const SCORE = {
  CE: CE,
  CO: CO,
};

export default function DetailHeaderC({ detailSet }: Props) {
  const resultSet = JSON.parse(detailSet.payload) as Resultset[];
  const CurentScore = SCORE[isCO(resultSet) ? 'CO' : 'CE'];
  const score = getScore(getTestSet(detailSet.serie), resultSet);
  const isTCF = Number(detailSet.serie.libelle) <= 999;
  return (
    <>
      <div className="w-ful flex h-14 items-center gap-4 overflow-x-scroll rounded-md border px-2 md:overflow-hidden">
        <Link
          href={`/examen/tcf/${detailSet.serie.libelle}`}
          className={buttonVariants({
            variant: 'default',
            class: 'min-w-[150px] md:min-w-[200px]',
          })}
        >
          Refaire ce test
        </Link>
        <CurentScore score={score} />
        <p className="min-w-[405px]">
          Effectué le{' '}
          {format(new Date(detailSet.createdAt), "dd/MM/yyyy' à 'HH:mm:ss")}
        </p>
      </div>
    </>
  );
}
