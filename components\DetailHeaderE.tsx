'use client';
import Link from 'next/link';
import React from 'react';
import { buttonVariants } from './ui/button';
import format from 'date-fns/format';
import { ResultatEE } from '@/types';
import { cn, getExpressionNiveaau, getNiveau } from '@/lib/utils';
interface Props {
  resultat: ResultatEE;
  mode: partialMode;
}

const EE = ({ score, isTCF }: { score: number; isTCF: Boolean }) => {
  return (
    <>
      {isTCF ? (
        <div className="flex min-w-[110px] flex-nowrap items-center justify-center gap-1 font-bold text-gray-600 md:min-w-[110px]">
          <p className="">EE</p>
          <span className="flex">
            <p
              className={`${score > 10 ? 'text-emerald-500' : 'text-red-500'}`}
            >
              {score}
            </p>
            /20
          </span>
          <p
            className={cn('rounded-sm border bg-zinc-100 px-3 py-2', {
              'text-red-500': score < 13,
              'text-green-400': score >= 13,
            })}
          >
            {getExpressionNiveaau(score)}
          </p>
        </div>
      ) : (
        <div className="flex min-w-[110px] flex-nowrap items-center justify-center gap-1 font-bold text-gray-600 md:min-w-[110px]">
          <p className="">PE</p>
          <span className="flex">
            <p
              className={cn('', {
                'text-red-500': score < 450,
                'text-green-400': score >= 450,
              })}
            >
              {score}
            </p>
            /699
          </span>
          <p
            className={cn('rounded-sm border bg-zinc-100 px-3 py-2', {
              'text-red-500': score < 450,
              'text-green-400': score >= 450,
            })}
          >
            {getNiveau(score)}
          </p>
        </div>
      )}
    </>
  );
};
const EO = ({ score, isTCF }: { score: number; isTCF: Boolean }) => {
  return (
    <>
      {isTCF ? (
        <div className="flex min-w-[110px] flex-nowrap items-center justify-center gap-1 font-bold text-gray-600 md:min-w-[110px]">
          <p className="">EO</p>
          <span className="flex">
            <p
              className={`${score > 10 ? 'text-emerald-500' : 'text-red-500'}`}
            >
              {score}
            </p>
            /20
          </span>
          <p
            className={cn('rounded-sm border bg-zinc-100 px-3 py-2', {
              'text-red-500': score < 13,
              'text-green-400': score >= 13,
            })}
          >
            {getExpressionNiveaau(score)}
          </p>
        </div>
      ) : (
        <div className="flex min-w-[110px] flex-nowrap items-center justify-center gap-1 font-bold text-gray-600 md:min-w-[110px]">
          <p className="">PO</p>
          <span className="flex">
            <p
              className={cn('', {
                'text-red-500': score < 450,
                'text-green-400': score >= 450,
              })}
            >
              {score}
            </p>
            /699
          </span>
          <p
            className={cn('rounded-sm border bg-zinc-100 px-3 py-2', {
              'text-red-500': score < 450,
              'text-green-400': score >= 450,
            })}
          >
            {getNiveau(score)}
          </p>
        </div>
      )}
    </>
  );
};

const SCORE = {
  EE: EE,
  EO: EO,
} as const;

type partialMode = 'EE' | 'EO';

export default function DetailHeaderE({ resultat, mode }: Props) {
  const CurentScore = SCORE[mode];
  const isTCF = Number(resultat.serie.libelle) <= 999;

  const score = resultat.resultat.reduce((acc, obj) => {
    return acc + obj.note;
  }, 0);

  return (
    <>
      <div className="w-ful flex h-14 items-center gap-4 overflow-x-scroll rounded-md border px-2 md:overflow-hidden">
        <Link
          href={`/examen/${isTCF ? 'tcf' : 'tef'}/${resultat.serie.libelle}`}
          className={buttonVariants({
            variant: 'default',
            class: 'min-w-[150px] md:min-w-[200px]',
          })}
        >
          Refaire ce test
        </Link>
        <CurentScore isTCF={isTCF} score={score} />
        <p className="min-w-[405px]">
          Effectué le{' '}
          {format(new Date(resultat.createdAt), "dd/MM/yyyy' à 'HH:mm:ss")}
        </p>
      </div>
    </>
  );
}
