'use client';
import { forwardRef } from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { cn } from '@/lib/utils';
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  title: string;
  description: string;
  isCancelable?: boolean;
}

const Dialog = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ onClick, className, isCancelable, title, description }, ref) => {
    return (
      <AlertDialog>
        <AlertDialogTrigger asChild>
          <button
            type="button"
            title="stop"
            className={cn('hidden', {}, className)}
            ref={ref}
          >
            blabla
          </button>
        </AlertDialogTrigger>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="text-center uppercase">
              {title}
            </AlertDialogTitle>
            <AlertDialogDescription asChild>
              <div className="w-full space-y-3 text-center font-semibold">
                <p className="">{description}</p>
              </div>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            {isCancelable ? <AlertDialogCancel>Non</AlertDialogCancel> : null}
            <AlertDialogAction onClick={onClick}>Oui</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  },
);

Dialog.displayName = 'dialog-button';

export default Dialog;
