'use client';
import React, { memo, useMemo } from 'react';
import QuestionComponent from './Question';
import 'react-sweet-progress/lib/style.css';
import { Resultat, Resultset } from '@/types';
const MemorizedQuestion = memo(QuestionComponent);
export default function DisplayQuestion({
  detailset,
}: {
  detailset: Resultat;
}) {
  const resulSet = JSON.parse(detailset.payload) as Resultset[];
  const memoquestion = useMemo(() => {
    return detailset.serie.questions
      .sort((a, b) => {
        return a.numero - b.numero;
      })
      .filter((q) => resulSet.find((r) => r.questionId == q.numero))
      .map((q) => (
        <MemorizedQuestion
          serieLib={detailset.serie.libelle}
          question={q}
          resulSet={resulSet}
          key={q.numero}
        />
      ));
  }, [detailset, resulSet]);
  return (
    <>
      <div className="mb-4 w-full space-y-5 md:px-3">{memoquestion}</div>
    </>
  );
}
