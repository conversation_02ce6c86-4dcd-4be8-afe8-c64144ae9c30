'use client';
import Link from 'next/link';
import { usePDF } from 'react-to-pdf';
import { Button, buttonVariants } from './ui/button';
import { getExpressionNiveaau, getNiveau } from '@/lib/utils';
import Image from 'next/image';
import { ResultatEE, ScoreC } from '@/types';
import format from 'date-fns/format';
import { useSession } from 'next-auth/react';
interface DowloadPDFProps {
  currentExpScore: number;
  LastExpScore: number | null;
  moy: string[];
  scoreCO: ScoreC | null;
  scoreCE: ScoreC | null;
  detailSet: ResultatEE;
  lastE: ResultatEE | undefined;
  mode: 'EE' | 'EO';
  slug: string;
}
export default function DowloadPDF({
  slug,
  currentExpScore,
  lastE,
  LastExpScore,
  scoreCE,
  scoreCO,
  moy,
  mode,
  detailSet,
}: DowloadPDFProps) {
  const { data: session } = useSession();
  const { toPDF, targetRef } = usePDF({
    filename: `${format(new Date(detailSet.createdAt), 'dd MMMM yyyy')}__resultat-serie-${detailSet.serie.libelle}.pdf`,
  });
  return (
    <>
      <div className="flex w-full flex-col gap-4 md:w-[60vw] md:px-5">
        <section
          ref={targetRef}
          className="mx-auto flex h-fit w-full flex-col gap-2 overflow-hidden rounded-sm border bg-white pb-8 drop-shadow-md"
          id="resultat"
        >
          <div className="flex w-full items-center justify-between">
            <Image
              quality={90}
              src={'/logo4.png'}
              alt="left"
              height={2482}
              width={2476}
              className="h-[75px] w-[150px] md:h-[150px] md:w-[300px]"
            />
            <Image
              quality={80}
              src={'/rigth-picture.png'}
              alt="rigth"
              height={100}
              width={300}
              className="h-[50px] w-[150px] md:h-[100px] md:w-[300px]"
            />
          </div>
          <div className="mx-auto text-center">
            <h2 className="text-sm font-medium md:text-lg">
              Resultat Test TCF en Ligne
            </h2>
            <h2 className="text-sm font-medium md:text-lg">
              Test de connaissance du français – Canada
            </h2>
          </div>
          <div className="mx-auto mt-5 flex w-full items-center gap-5 px-3 md:justify-between md:gap-0 md:px-20">
            <div className="flex flex-col gap-[2px] text-xs md:gap-1 md:text-base">
              <div className="flex items-center">
                <p>Email:</p>
                <p className="ml-1 font-bold">{session?.user.email}</p>
              </div>
              <div className="flex items-center">
                <p>Id-candidat:</p>
                <p className="font-bold md:ml-1"> {session?.user._id}</p>
              </div>
              <div className="flex items-center">
                <p>Date:</p>
                <p className="ml-1 font-bold">
                  {format(new Date(detailSet.createdAt), 'dd MMMM yyyy')}
                </p>
              </div>
              <div className="flex items-center">
                <p>URL:</p>
                <Link href={'/'} className="ml-1 text-blue-500">
                  objectifcanada-tcf.com
                </Link>
              </div>
            </div>
            <div className="flex flex-col md:mr-10">
              <div className="mx-auto text-center">
                <h2 className="font-black md:text-2xl">Série</h2>
                <h2 className="font-black md:text-2xl">
                  {detailSet.serie?.libelle}
                </h2>
              </div>
            </div>
          </div>
          <div className="mx-auto w-[95%] border-0 border-b-4 border-black md:w-[90%]" />
          <p className="ml-5 text-sm font-bold md:ml-16 md:text-base">
            Résultat aux épreuves
          </p>
          <div className="flex w-full flex-col justify-between md:flex-row md:items-center md:px-16">
            <div className="flex flex-col gap-8 px-3 text-sm md:w-[80%] md:pr-5 md:text-base">
              <div className="w-full">
                <div className="flex h-16 w-full border border-black">
                  <div className="h-full w-full p-2 font-bold">
                    Epreuves QCM
                  </div>
                  <div className="h-full w-full border-0 border-x border-black p-2 font-bold">
                    <p>Score</p>
                    <p>/699</p>
                  </div>
                  <div className="h-full w-full p-2 font-bold">
                    <p> Niveau</p>
                    <p>CECRL</p>
                  </div>
                </div>
                <div className="flex h-10 w-full border border-b-0 border-t-0 border-black">
                  <div className="h-full w-full p-2">Comp orale</div>
                  <div className="h-full w-full border-0 border-x border-black p-2">
                    {scoreCO?.CO}
                  </div>
                  <div className="h-full w-full p-2">
                    {scoreCO ? getNiveau(scoreCO.CO!) : ''}
                  </div>
                </div>
                <div className="flex h-10 w-full border border-black">
                  <div className="h-full w-full p-2">Comp écrite</div>
                  <div className="h-full w-full border-0 border-x border-black p-2">
                    {scoreCE?.CE}
                  </div>
                  <div className="h-full w-full p-2">
                    {scoreCE ? getNiveau(scoreCE.CE!) : ''}
                  </div>
                </div>
              </div>
              <div className="w-full">
                <div className="flex h-16 w-full border border-black">
                  <div className="h-full w-full p-2 font-bold">
                    <p>Epreuves</p>
                    <p>d’expression</p>
                  </div>
                  <div className="h-full w-full border-0 border-x border-black p-2 font-bold">
                    <p>Score</p>
                    <p>/20</p>
                  </div>
                  <div className="h-full w-full p-2 font-bold">
                    <p> Niveau</p>
                    <p>CECRL</p>
                  </div>
                </div>
                <div className="flex h-10 w-full border border-b-0 border-t-0 border-black">
                  <div className="h-full w-full p-2">Expr orale </div>
                  <div className="h-full w-full border-0 border-x border-black p-2">
                    {mode === 'EO'
                      ? currentExpScore
                      : lastE
                        ? LastExpScore
                        : null}
                  </div>
                  <div className="h-full w-full p-2">
                    {mode === 'EO'
                      ? getExpressionNiveaau(currentExpScore)
                      : lastE
                        ? getExpressionNiveaau(LastExpScore!)
                        : null}
                  </div>
                </div>
                <div className="flex h-10 w-full border border-black">
                  <div className="h-full w-full p-2">Expr écrite</div>
                  <div className="h-full w-full border-0 border-x border-black p-2">
                    {mode === 'EE'
                      ? currentExpScore
                      : lastE
                        ? LastExpScore
                        : null}
                  </div>
                  <div className="h-full w-full p-2">
                    {mode === 'EE'
                      ? getExpressionNiveaau(currentExpScore)
                      : lastE
                        ? getExpressionNiveaau(LastExpScore!)
                        : null}
                  </div>
                </div>
              </div>
            </div>
            <div className="mt-3 flex flex-col md:mt-0">
              <div className="mx-auto flex gap-2 text-center md:block">
                <h2 className="text-2xl font-black">Niveau</h2>
                <h2 className="text-2xl font-black">{moy[0]}</h2>
              </div>
            </div>
          </div>
        </section>
        <div className="flex flex-col items-center justify-around gap-4 md:w-[60vw] md:flex-row">
          <Link
            href={`/detailC/${mode}/${slug}`}
            className={buttonVariants({ variant: 'default' })}
          >
            Voir les details
          </Link>
          <Button onClick={() => toPDF()}>Download PDF</Button>
        </div>
      </div>
    </>
  );
}
