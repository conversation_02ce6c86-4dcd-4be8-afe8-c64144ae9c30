'use client';

import { EOPayload, EOTask, ResultatEE } from '@/types';
import { useState } from 'react';
import { Button, buttonVariants } from './ui/button';
import { getEOurl, getEOurlTEF, getMaxNote, getMaxNoteTEF } from '@/lib/utils';
import {
  BUCKET_BASE_EO_URL,
  BUCKET_BASE_URL,
  EO_SECTION_A_FILENAME,
  EO_SECTION_B_FILENAME,
} from '@/config';
import Link from 'next/link';
import logo from '@/public/logo4.png';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog';
import { ScrollArea } from './ui/scroll-area';
import parse from 'html-react-parser';
import ImageZoomInOut from './ZoumInOutImage';
import NewWrapper from './new-wrapper';

interface EOCardTCFProps {
  payload: EOPayload;
  task: EOTask;
  detailSet: ResultatEE;
}
export function EOCardTCF({ task, payload, detailSet }: EOCardTCFProps) {
  const [showConsigne, setShowConsigne] = useState(true);
  const handleEyes = () => {
    const newSate = !showConsigne;
    setShowConsigne(newSate);
  };

  const comment =
    detailSet.resultat.find(
      (res) => res.task === task.libelle.replaceAll(' ', ''),
    )?.comment || '';
  const url = getEOurl(task.numero, payload);
  const max = getMaxNote(task.libelle);
  const moy = max / 2;
  const score =
    detailSet.resultat.find(
      (res) => res.task === task.libelle.replaceAll(' ', ''),
    )?.note || 0;
  return (
    <div className="bgc mb-3 flex h-fit flex-col items-start justify-between rounded-sm border-zinc-300 py-2 pt-4 bg-size md:border md:px-5 lg:flex-row">
      <div className="flex flex-col gap-1 text-center font-semibold">
        {showConsigne ? (
          <>
            <div className="flex gap-5">
              <h1 className="text-base capitalize underline underline-offset-4">
                {task.libelle}
              </h1>
              <span className="flex">
                <p
                  className={`${
                    score >= moy ? 'text-emerald-500' : 'text-red-500'
                  }`}
                >
                  {score}
                </p>
                /{max}
              </span>
            </div>
            <div className="prose mt-5 max-w-[50ch] text-justify font-normal">
              {parse(task.consigne)}
            </div>
          </>
        ) : (
          <>
            <audio controls src={`${BUCKET_BASE_URL}/${task.fichier}`} />
          </>
        )}

        <div className="flex items-center gap-2">
          {task.fichier !== '' ? (
            <Button
              size={'default'}
              className="!max-w-[200px]"
              onClick={handleEyes}
            >
              {showConsigne ? 'Ecouter la consigne' : 'Afficher la consigne'}
            </Button>
          ) : null}
          {detailSet.resultat.find(
            (res) => res.task === task.libelle.replaceAll(' ', ''),
          )?.comment != null || undefined ? (
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  size={'default'}
                  className="!max-w-[200px] bg-orange-400"
                >
                  Voir la correction
                </Button>
              </DialogTrigger>
              <DialogContent className="overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Correction</DialogTitle>
                  <DialogDescription className="prose max-h-[400px] text-left">
                    <ScrollArea>
                      <div id="prose">{parse(comment)}</div>
                    </ScrollArea>
                  </DialogDescription>
                </DialogHeader>
              </DialogContent>
            </Dialog>
          ) : null}
        </div>
      </div>
      <div className="px-5">
        <div className="flex justify-end gap-5">
          <Link
            className={buttonVariants({ variant: 'link' })}
            href={`/correction/TCF/EO/${detailSet.serie.libelle}`}
          >
            <NewWrapper>Voir un exemple</NewWrapper>
          </Link>
        </div>
        <h1 className="mb-2 text-base font-bold">Votre réponse</h1>
        <div className=" ">
          {url ? (
            <audio controls src={`${BUCKET_BASE_EO_URL}/${url}`} />
          ) : (
            <h2>Vous n&apos;avez pas soumis de reponse</h2>
          )}
        </div>
      </div>
    </div>
  );
}

interface EOCardTEFProps {
  payload: EOPayloadTEF;
  section: SectionEO;
  detailSet: any;
}
export function EOCardTEF({ section, payload, detailSet }: EOCardTEFProps) {
  const [showConsigne, setShowConsigne] = useState(true);
  const handleEyes = () => {
    const newSate = !showConsigne;
    setShowConsigne(newSate);
  };
  const matchSection = section.libelle.replaceAll(' ', '').includes('A')
    ? 'section1'
    : 'section2';
  const fileName =
    matchSection == 'section1' ? EO_SECTION_A_FILENAME : EO_SECTION_B_FILENAME;
  const comment =
    detailSet.resultat.find((res: any) => res.section === matchSection)
      ?.comment || '';

  const url = getEOurlTEF(section.numero, payload as any);
  const max = getMaxNoteTEF(section.libelle);
  const moy = max / 2;
  const score =
    detailSet.resultat.find((res: any) => res.section === matchSection)?.note ||
    0;

  return (
    <div className="bgc mb-3 flex h-fit flex-col items-start justify-between rounded-sm border-zinc-300 py-2 pt-4 bg-size md:border md:px-5 lg:flex-row">
      <div className="flex w-full flex-col gap-1 text-center font-semibold md:w-1/2">
        <div className="flex gap-5">
          <h1 className="text-base capitalize underline underline-offset-4">
            {section.libelle}
          </h1>
          <span className="flex">
            <p
              className={`${
                score >= moy ? 'text-emerald-500' : 'text-red-500'
              }`}
            >
              {score}
            </p>
            /{max}
          </span>
        </div>
        <div className="prose mt-5 max-w-[50ch] text-justify font-normal">
          {parse(section.consigne)}
        </div>
        {!showConsigne && <audio controls src={`/audios/${fileName}`} />}

        <div className="flex items-center gap-2">
          <Button
            size={'default'}
            className="!max-w-[200px]"
            onClick={handleEyes}
          >
            {showConsigne ? 'Ecouter la consigne' : 'Masquer'}
          </Button>
          {detailSet.resultat.find((res: any) => res.section === matchSection)
            ?.comment ? (
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  size={'default'}
                  className="!max-w-[200px] bg-orange-400"
                >
                  Voir la correction
                </Button>
              </DialogTrigger>
              <DialogContent className="overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Correction</DialogTitle>
                  <DialogDescription className="prose max-h-[400px] text-left">
                    <ScrollArea>
                      <div id="prose">{parse(comment)}</div>
                    </ScrollArea>
                  </DialogDescription>
                </DialogHeader>
              </DialogContent>
            </Dialog>
          ) : null}
        </div>
      </div>
      <div className="w-full px-5 md:w-1/2">
        <div className="flex justify-end gap-5">
          <Link
            className={buttonVariants({ variant: 'link' })}
            href={`/correction/TEF/EO/${detailSet.serie.libelle}`}
          >
            <NewWrapper>Voir un exemple</NewWrapper>
          </Link>
        </div>
        <h1 className="mb-2 text-base font-bold">Votre réponse</h1>
        <div className=" ">
          {url ? (
            <audio controls src={`${BUCKET_BASE_EO_URL}/${url}`} />
          ) : (
            <h2>Vous n&apos;avez pas soumis de reponse</h2>
          )}
        </div>
        <div className="mt-2 md:w-full">
          {section.images?.map((img, i) => (
            <ImageZoomInOut
              imageUrl={
                img.trim().length > 0 ? `${BUCKET_BASE_URL}/${img}` : logo
              }
              placeholder={img.trim().length > 0 ? 'empty' : 'blur'}
              key={i}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
