import React from 'react';
interface Props {
  description?: string;
  title?: string;
  sub?: string;
}
export default function ErrorComponent({ description, title, sub }: Props) {
  return (
    <div className="grid h-fit w-fit max-w-fit place-content-center bg-white px-4">
      <div className="text-center">
        <h1 className="mb-2 text-5xl font-black text-gray-200 md:text-7xl">
          {title}
        </h1>

        <p className="text-2xl font-bold tracking-tight text-gray-900 sm:text-4xl">
          {sub}
        </p>

        <p className="mt-4 text-gray-500">{description}</p>
      </div>
    </div>
  );
}
