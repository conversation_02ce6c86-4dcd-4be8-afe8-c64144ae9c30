'use client';
import { usePathname } from 'next/navigation';
import React from 'react';
import Links from './Link';

export default function Footer() {
  const path = usePathname()!;
  return (
    <footer
      className={`h-fit bg-blue-500 ${
        path?.includes('/examen/') || path?.includes('test') ? 'hidden' : ''
      } w-full overflow-hidden`}
    >
      <div className="mx-auto px-4 py-1 lg:max-w-screen-xl lg:px-8">
        <div className="flex flex-col items-center justify-between md:flex-row">
          <div className="flex justify-center text-white sm:justify-start">
            <h2 className={`text-2xl font-bold`}>Objectif Canada</h2>
          </div>
          <Links />
          <p className="text-center text-sm text-white lg:mt-0 lg:text-right">
            Copyright &copy; {new Date().getFullYear()}. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
