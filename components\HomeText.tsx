'use client';
import { useTranslation } from '@/app/i18n/client';
import { usei18nState } from '@/context/i18n';
import { cn } from '@/lib/utils';
import { useDocumentTitle } from '@mantine/hooks';

export default function HomeText() {
  const { lng } = usei18nState();
  const { t } = useTranslation(lng);
  useDocumentTitle(`TCF | ${t('title')}`);

  return (
    <>
      <h1
        className={cn(
          'isolation animate__animated animate__fadeInUp ml-[1.5rem] text-2xl font-extrabold text-gray-900 sm:text-5xl lg:ml-[6rem] lg:w-fit',
          {},
        )}
      >
        Le goût des{' '}
        <span className="bg-gradient-to-r from-green-300 via-blue-500 to-purple-600 bg-clip-text text-transparent">
          C2
        </span>
        <br />
        <span className="mt-1 sm:block lg:w-fit"> {t('subtitle')} </span>
      </h1>

      <p className="mx-auto mt-4 max-w-sm text-sm/relaxed md:max-w-fit md:text-xl/relaxed">
        {t('home-text')}
      </p>
    </>
  );
}
