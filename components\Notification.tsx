'use client';

import { <PERSON>, <PERSON> } from 'lucide-react';
import ReactTime<PERSON>go from 'react-time-ago';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import Skeleton from 'react-loading-skeleton';
import { buttonVariants } from './ui/button';
import Link from 'next/link';
import { useUpdateIsView } from '@/hooks/use-updateView';
import { useCallback, useEffect, useState } from 'react';
import { toast as SonnerToast, toast } from 'sonner';
import { usePathname } from 'next/navigation';
import { ScrollArea } from './ui/scroll-area';
import { useClickOutside } from '@mantine/hooks';
import { useGetExpressionResult } from '@/lib/hooks';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

//DISTINCTION ENTRE TEF ET TCF

export default function Notification() {
  const { data, isLoading, refetch } = useGetExpressionResult();
  const { mutate: updateIsView } = useUpdateIsView({
    onSuccess: async () => {
      await refetch();
    },
  });
  const [intervalPrep, setIntervalPrep] = useState<NodeJS.Timer | null>(null);
  const [showNotifPopup, setshowNotifPopup] = useState(false);
  const path = usePathname();
  const ref = useClickOutside(() => {
    setshowNotifPopup(false);
  });
  const startToasting = useCallback(() => {
    const interval = setInterval(
      () => {
        if (
          !path.includes('/examen/tcf/') &&
          !path.includes('/examen/tef/') &&
          !path.includes('/dashboard')
        ) {
          SonnerToast.info('Vous avez des resultats non lu', {
            position: 'bottom-left',
            description: 'Allez vite les voir',
            action: {
              label: 'Voir la correction',
              actionButtonStyle: {
                marginLeft: 'auto',
              },
              onClick: () => {
                SonnerToast.dismiss();
              },
            },
            dismissible: false,
            duration: 1000 * 60 * 2,
          });
        }
      },
      1000 * 60 * 5,
    );
    return interval;
  }, []);

  useEffect(() => {
    if (data?.length && !isLoading) {
      setIntervalPrep(startToasting());
    } else if (!data?.length && !isLoading) {
      clearInterval(intervalPrep!);
    }
  }, [data]);

  return (
    <Popover open={showNotifPopup}>
      <TooltipProvider disableHoverableContent>
        <Tooltip delayDuration={100}>
          <TooltipTrigger asChild>
            <PopoverTrigger asChild>
              <span
                onClick={() => setshowNotifPopup((prev) => !prev)}
                className={buttonVariants({
                  variant: 'ghost',
                  className:
                    'flex !h-8 w-fit cursor-pointer gap-2 !rounded-full border !p-2',
                })}
              >
                <Bell className="h-4 w-4 font-light text-gray-500" />
                {isLoading ? null : data?.length! > 0 ? (
                  <span className="flex h-5 w-5 animate-buttonheartbeat items-center justify-center rounded-full bg-blue-500 p-2 text-xs text-white">
                    {data?.length}
                  </span>
                ) : null}
              </span>
            </PopoverTrigger>
          </TooltipTrigger>
          <TooltipContent side="bottom">Notifications</TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <PopoverContent
        sideOffset={10}
        align="start"
        className="max-h-[80vh] min-h-fit overflow-y-auto"
      >
        <ScrollArea ref={ref} className="">
          {isLoading ? null : data !== undefined && data.length > 0 ? (
            data
              .sort((a, b) => {
                const d: any = new Date(a.createdAt);
                const c: any = new Date(b.createdAt);
                return c - d;
              })
              .map((result) => (
                <Link
                  onClick={() => {
                    if (result.isView == false)
                      updateIsView({
                        result,
                        mode: result.type,
                        examen: result.examen == 'TEF' ? 'tef/' : '',
                      });
                    toast.dismiss();
                  }}
                  href={`/detailC/${result.examen}/${result.type}/${result._id}`}
                  key={result._id}
                  className="mb-1 flex cursor-pointer flex-col space-y-1 rounded-md border bg-slate-100 p-2 text-sm font-semibold"
                >
                  <div className="flex gap-2">
                    <p>serie : {result?.serie?.libelle}</p>
                    <p className="ml-auto text-sm text-gray-500">
                      <ReactTimeAgo
                        locale="fr-FR"
                        date={new Date(result.resultat[0].createdAt).getTime()}
                      />
                    </p>
                  </div>
                  <p className="font-normal">{`resultat d'experssion ${
                    result.type == 'EE' ? 'ecrite' : 'orale'
                  }  disponible`}</p>
                </Link>
              ))
          ) : (
            <div className="flex items-center gap-2">
              <Ghost className="h-8 w-8 text-gray-400" />
              <p className="font-bold text-gray-500">Aucun test récent</p>
            </div>
          )}
          {isLoading && <Skeleton height={50} count={3} />}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
