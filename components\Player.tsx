'use client';
import 'react-sweet-progress/lib/style.css';
import { Loader2, Pause, Play } from 'lucide-react';
import { useEffect, useState } from 'react';
import useSound from 'use-sound';
import Progress from '@/lib/jscomponent';
export default function Player({ soundURL }: { soundURL: string }) {
  const [percent, setPercent] = useState(100);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isSoundLoading, setIsSoundLoading] = useState(true);
  const [play, { duration, sound, pause, stop }] = useSound(soundURL, {
    onload: () => {
      setIsSoundLoading(false);
    },
    html5: true,
  });
  const theme = {
    error: {
      symbol: ' ',
      trailColor: '',
      color: 'red',
    },
    default: {
      symbol: ' ',
      trailColor: '',
      color: 'blue',
    },
    active: {
      symbol: ' ',
      trailColor: '',
      color: 'orange',
    },
    success: {
      symbol: ' ',
      trailColor: '',
      color: 'green',
    },
  };

  const status = (percent: number): string => {
    let res = '';
    if (percent >= 67) {
      res = 'success';
    }

    if (percent < 67 && percent >= 34) {
      res = 'active';
    }

    if (percent < 34) {
      res = 'error';
    }

    return res;
  };

  const togglePlay = () => {
    if (!isPlaying) {
      setIsPlaying(true);
      play();
    } else {
      setIsPlaying(false);
      pause();
    }
  };
  useEffect(() => {
    if (isPlaying && percent > 0) {
      setTimeout(
        () => setPercent(100 - (sound?.seek()! / sound?.duration()!) * 100),
        50,
      );
    }
    if (Math.round(percent) === 0) {
      stop();
    }
  }, [percent, isPlaying, sound, stop]);

  return (
    <div className="flex w-full items-center gap-5 bg-white p-1">
      <button
        aria-label="play-pause"
        onClick={togglePlay}
        className="flex items-center justify-center rounded-sm bg-blue-500 p-2 text-white"
      >
        {isSoundLoading ? (
          <Loader2 className="animate-spin" />
        ) : (
          <>{isPlaying ? <Pause /> : <Play />}</>
        )}
      </button>
      <Progress theme={theme} percent={percent} status={status(percent)} />
    </div>
  );
}
