'use client';
import { getPoint, isCO } from '@/lib/utils';
import { Question, Resultset } from '@/types';
import Image from 'next/image';
import Link from 'next/link';
import React, { useState } from 'react';
import { BUCKET_BASE_URL } from '@/config';
import { Button } from './ui/button';
import { Eye, X } from 'lucide-react';
import { Dialog, DialogContent, DialogTrigger } from './ui/dialog';
import ImageZoomInOut from './ZoumInOutImage';
import left from '@/public/left.png';
import logo from '@/public/logo4.png';

export default function QuestionComponent({
  question,
  resulSet,
  serieLib,
}: {
  serieLib: string;
  question: Question;
  resulSet: Resultset[];
}) {
  const [showPalayer, setShowPlayer] = useState(false);
  const userSugestionId = resulSet.find((q) => q.questionId == question.numero);
  const userSuggestion = question.suggestions.find(
    (s) => s._id == userSugestionId?.resId,
  );
  const goodSuggestion = question.suggestions.find((s) => s.isCorrect == true);
  // const {mode} = useTestState()
  const mode = isCO(resulSet) ? 'CO' : 'CE';
  return (
    <>
      <div className="flex h-fit flex-col items-center gap-5 rounded-md p-3 md:flex-row">
        <div className="group relative mb-6 h-96 w-full overflow-hidden rounded-lg shadow-lg">
          <Image
            alt="image-question"
            src={
              question?.libelle?.trim()?.length! > 0
                ? `${BUCKET_BASE_URL}/${question?.libelle!}`
                : logo
            }
            fill
            priority
            className={'object-contain'}
          />
          <Dialog>
            <DialogTrigger asChild>
              <Button
                variant={'secondary'}
                size={'icon'}
                className="absolute left-2 top-2"
              >
                <Eye className="text-blue-400 hover:text-blue-600" />
              </Button>
            </DialogTrigger>
            <DialogContent className="h-96 w-full max-w-xl md:max-w-2xl">
              <ImageZoomInOut
                placeholder="empty"
                imageUrl={
                  question?.libelle?.trim()?.length! > 0
                    ? `${BUCKET_BASE_URL}/${question?.libelle!}`
                    : '/logo4.png'
                }
              />
            </DialogContent>
          </Dialog>
        </div>
        <div className="relative flex w-full flex-col gap-4 overflow-hidden rounded-md p-1 text-sm md:mt-0 md:min-h-[200px] md:p-4">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <span
                className={`${
                  userSuggestion?.isCorrect ? 'bg-blue-500' : 'bg-red-500'
                } flex h-10 w-16 items-center justify-center rounded-md p-4 text-base font-bold text-white md:text-lg`}
              >
                {question?.numero}
              </span>
              <span
                className={`flex h-10 items-center justify-center rounded-md bg-yellow-100 p-4 text-base font-bold md:text-lg`}
              >
                Serie {serieLib}
              </span>
            </div>
            <Link href={'#'} className="text-blue-500 underline">
              Aide ?
            </Link>
          </div>
          <div className="flex items-center gap-8">
            {mode === 'CE' ? (
              <p className="">{question.consigne}</p>
            ) : (
              <>
                {showPalayer ? (
                  <div className="relative !w-full">
                    <audio
                      controls
                      src={`${BUCKET_BASE_URL}/${question.consigne}`}
                    />
                    <Button
                      variant={'outline'}
                      size={'icon'}
                      onClick={() => setShowPlayer(false)}
                      className="absolute -top-6 right-0 rounded-full border-2 !border-dashed p-1"
                    >
                      <X className="h-4 w-4 text-red-500" />
                    </Button>
                  </div>
                ) : (
                  <Button
                    variant={'outline'}
                    onClick={() => setShowPlayer(true)}
                    className="mx-auto w-fit"
                  >
                    <span className="text-blue-500">Écouter la consigne</span>
                  </Button>
                )}
              </>
            )}
            <p
              className={`text-2xl font-bold ${
                userSuggestion?.isCorrect ? 'text-emerald-500' : 'text-red-500'
              }`}
            >
              +{userSuggestion?.isCorrect ? getPoint(question.numero) : 0}
            </p>
          </div>
          <div className="flex flex-col gap-1 md:flex-row md:items-start">
            <p className="">Votre réponse :</p>
            <p
              className={`max-w-[30ch] font-bold ${
                userSuggestion?.isCorrect ? 'text-emerald-500' : 'text-red-500'
              } `}
            >
              {userSuggestion?.text}
            </p>
          </div>
          <div className="flex flex-col gap-1 md:flex-row md:items-start">
            <p className="">La réponse juste :</p>
            <p className={`max-w-[30ch] font-bold`}>{goodSuggestion?.text}</p>
          </div>
          <Image
            alt="logo"
            width={70}
            height={70}
            src={left}
            loading="lazy"
            className="absolute bottom-2 right-4"
          />
        </div>
      </div>
    </>
  );
}
