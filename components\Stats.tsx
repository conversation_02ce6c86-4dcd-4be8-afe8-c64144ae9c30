'use client';
import { Separator } from './ui/separator';

export const Statistic = () => {
  return (
    <div className="mx-auto max-w-[100vw] px-4 py-16 sm:max-w-xl md:max-w-full md:px-24 lg:lg:max-w-screen-xl lg:px-8 lg:py-20">
      <div className="row-gap-8 grid gap-14 lg:grid-cols-5">
        <div>
          <div className="flex">
            <h6 className="mr-2 text-4xl font-bold tabular-nums tracking-tight md:text-5xl">
              + 40
            </h6>
            <div className="flex h-7 w-7 items-center justify-center rounded-full bg-blue-400">
              <svg
                className="h-7 w-7 text-teal-900"
                stroke="currentColor"
                viewBox="0 0 52 52"
              >
                <polygon
                  strokeWidth="3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  fill="none"
                  points="29 13 14 29 25 29 23 39 38 23 27 23"
                />
              </svg>
            </div>
          </div>
          <p className="mb-2 break-words font-bold md:text-lg">
            Séries de tests complètes
          </p>
        </div>
        <Separator orientation="vertical" className="mx-auto hidden lg:block" />
        <div>
          <div className="flex">
            <h6 className="mr-2 text-4xl font-bold tabular-nums tracking-tight md:text-5xl">
              + 05
            </h6>
            <div className="flex h-7 w-7 items-center justify-center rounded-full bg-blue-400">
              <svg
                className="h-7 w-7 text-teal-900"
                stroke="currentColor"
                viewBox="0 0 52 52"
              >
                <polygon
                  strokeWidth="3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  fill="none"
                  points="29 13 14 29 25 29 23 39 38 23 27 23"
                />
              </svg>
            </div>
          </div>
          <p className="mb-2 break-words font-bold md:text-lg">
            Examinateurs professionnels
          </p>
        </div>
        <Separator orientation="vertical" className="mx-auto hidden lg:block" />
        <div>
          <div className="flex">
            <h6 className="mr-2 text-4xl font-bold tabular-nums tracking-tight md:text-5xl">
              04
            </h6>
            <div className="flex h-7 w-7 items-center justify-center rounded-full bg-blue-400">
              <svg
                className="h-7 w-7 text-teal-900"
                stroke="currentColor"
                viewBox="0 0 52 52"
              >
                <polygon
                  strokeWidth="3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  fill="none"
                  points="29 13 14 29 25 29 23 39 38 23 27 23"
                />
              </svg>
            </div>
          </div>
          <p className="mb-2 font-bold md:text-lg">Epreuves par série</p>
        </div>
      </div>
    </div>
  );
};
