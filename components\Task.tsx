'use client';
import logo from '@/public/logo4.png';
import parse from 'html-react-parser';

import { useState } from 'react';
import ImageZoomInOut from './ZoumInOutImage';
import { Button } from './ui/button';
import { BUCKET_BASE_URL } from '@/config';
import Editor from './editor';

type TaskType = {
  _id: string;
  numero: number;
  libelle: string;
  typeProduction: string;
  consigne: string;
  images: string[] | null;
  minWord: number;
  maxWord: number;
};
interface Props {
  num: number;
  text: string;
  task: TaskType;
}

export default function Task({ num, text, task }: Props) {
  const [showConsigne, setShowConsigne] = useState(true);

  const handleEyes = () => {
    const newSate = !showConsigne;
    setShowConsigne(newSate);
  };

  return (
    <div className="flex h-fit flex-col items-start justify-around gap-5 pt-4 md:px-8">
      <div className="flex w-full flex-col gap-1 text-center font-semibold">
        {showConsigne ? (
          <>
            <h1 className="text-base capitalize underline underline-offset-4">
              {task.libelle}
            </h1>
            <p>{task.minWord} Mots minimum</p>
            <p>{task.maxWord} Mots maximum</p>
            <div className="prose mx-auto mt-5 max-w-5xl text-justify font-normal">
              {parse(task.consigne)}
            </div>
          </>
        ) : (
          <>
            {task.images?.map((img, i) => (
              <ImageZoomInOut
                imageUrl={
                  img.trim().length > 0 ? `${BUCKET_BASE_URL}/${img}` : logo
                }
                placeholder={img.trim().length > 0 ? 'empty' : 'blur'}
                key={i}
              />
            ))}
          </>
        )}

        {task.images?.length && task.images?.length > 0 ? (
          <Button className="mx-auto my-2 max-w-sm" onClick={handleEyes}>
            {showConsigne
              ? 'Afficher les pieces jointes'
              : 'Afficher la consigne'}
          </Button>
        ) : null}
      </div>
      <div className="w-full">
        <Editor num={num} text={text} />
      </div>
    </div>
  );
}
