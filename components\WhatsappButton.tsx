'use client';

import Image from 'next/image';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { X } from 'lucide-react';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import { BsWhatsapp } from 'react-icons/bs';
import Link from 'next/link';
import { useClickOutside } from '@mantine/hooks';
import { WHATSAPP_LINK } from '@/config';
import whatsapp from '@/public/whatsapp.svg';
export default function WhatsappButton() {
  const [showX, setshowX] = useState(false);
  const ref = useClickOutside(() => setshowX(false));
  return (
    <div ref={ref}>
      <Popover open={showX}>
        <PopoverTrigger asChild>
          <div
            onClick={() => {
              setshowX((prev) => !prev);
            }}
            className="fixed bottom-10 right-3 z-[99] flex cursor-pointer items-center justify-center rounded-full bg-[#4ac959] p-3 shadow-md drop-shadow-lg lg:right-10"
          >
            <X
              className={cn(
                'animate__animated absolute mx-auto h-[30px] w-[30px] text-white',
                {
                  animate__rotateOut: !showX,
                  animate__rotateIn: showX,
                },
              )}
            />
            <span
              className={cn('animate__animated text-4xl text-white', {
                'animate__rotateOut animate__fast': showX,
                'animate__rotateIn animate__fast': !showX,
              })}
            >
              <BsWhatsapp />
            </span>
          </div>
        </PopoverTrigger>
        <PopoverContent
          align="start"
          sideOffset={10}
          className={cn(
            'animate__animated w-[350px] overflow-hidden rounded-md p-0 hover:shadow-xl hover:drop-shadow-xl md:mr-5',
            {
              'animate__fadeInUp animate__faster': showX,
              'animate__fadeOutDown animate__fast': !showX,
            },
          )}
        >
          <div className="flex gap-5 bg-[#4ac959] p-4 text-white">
            <span className="text-4xl">
              <BsWhatsapp />
            </span>
            <p className="text-lg">
              Commencer une <br /> conversation
            </p>
          </div>
          <div className="flex flex-col gap-5 bg-white p-4">
            <Link
              href={'https://wa.me/237686876873'}
              target="_blank"
              className="animate__animated animate__fadeInUp flex items-center justify-between rounded-bl-sm rounded-tr-sm border-0 border-l-2 border-l-green-700 bg-zinc-200 px-2 py-3"
            >
              <div className="flex items-center gap-5">
                <Image
                  src={whatsapp}
                  width={50}
                  height={50}
                  alt="whatsapp icon"
                />
                <span className="text-gray-700">
                  Assistance <br />{' '}
                  <p className="text-xs text-gray-500">Service client</p>
                </span>
              </div>
              <span className="text-xl text-green-500">
                <BsWhatsapp />
              </span>
            </Link>
            <Link
              href={WHATSAPP_LINK}
              target="_blank"
              className="animate__animated animate__fadeInUp flex items-center justify-between rounded-bl-sm rounded-tr-sm border-0 border-l-2 border-l-green-700 bg-zinc-200 px-2 py-3"
            >
              <div className="flex items-center gap-5">
                <Image
                  src={whatsapp}
                  width={50}
                  height={50}
                  alt="whatsapp icon"
                />
                <span className="text-gray-700">
                  Forum de préparation <br />{' '}
                  <p className="text-xs text-gray-500">
                    Rejoignez les préparateurs et les autres candidats du monde
                    entier
                  </p>
                </span>
              </div>
              <span className="text-xl text-green-500">
                <BsWhatsapp />
              </span>
            </Link>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}
