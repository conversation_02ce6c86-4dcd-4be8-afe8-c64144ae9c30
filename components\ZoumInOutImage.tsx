/* eslint-disable @next/next/no-img-element */
'use  client';
import { IterationCw, Minus, Plus } from 'lucide-react';
import Image, { StaticImageData } from 'next/image';
import React, { useState, useRef, useEffect } from 'react';

function ImageZoomInOut({
  imageUrl,
  placeholder,
}: {
  imageUrl: string | StaticImageData;
  placeholder: 'blur' | 'empty';
}) {
  const [scale, setScale] = useState(1);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const imageRef = useRef<HTMLImageElement>(null);

  const handleZoomIn = () => {
    setScale((scale) => scale + 0.1);
  };

  const handleZoomOut = () => {
    setScale((scale) => scale - 0.1);
  };
  const handleReset = () => {
    setScale(1);
    setPosition({ x: 0, y: 0 });
  };
  useEffect(() => {
    const image = imageRef.current;
    let isDragging = false;
    let prevPosition = { x: 0, y: 0 };

    const handleMouseDown = (e: MouseEvent) => {
      isDragging = true;
      prevPosition = { x: e.clientX, y: e.clientY };
    };

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging) return;
      const deltaX = e.clientX - prevPosition.x;
      const deltaY = e.clientY - prevPosition.y;
      prevPosition = { x: e.clientX, y: e.clientY };
      setPosition((position) => ({
        x: position.x + deltaX,
        y: position.y + deltaY,
      }));
    };

    const handleMouseUp = () => {
      isDragging = false;
    };

    image?.addEventListener('mousedown', handleMouseDown);
    image?.addEventListener('mousemove', handleMouseMove);
    image?.addEventListener('mouseup', handleMouseUp);

    return () => {
      image?.removeEventListener('mousedown', handleMouseDown);
      image?.removeEventListener('mousemove', handleMouseMove);
      image?.removeEventListener('mouseup', handleMouseUp);
    };
  }, [imageRef, scale]);

  return (
    <div className="relative grid place-content-center overflow-hidden rounded-[10px] bg-white shadow-md">
      <div className="boder absolute left-0 top-0 z-20 w-fit bg-white p-1 text-black shadow-md md:p-2">
        <button
          title="add"
          className="flex items-center justify-center p-2"
          onClick={handleZoomIn}
        >
          <Plus className="h-3 w-3 md:h-6 md:w-6" />
        </button>
        <button
          title="remove"
          className="flex items-center justify-center p-2"
          onClick={handleZoomOut}
        >
          <Minus className="h-3 w-3 md:h-6 md:w-6" />
        </button>
        <button
          title="reset"
          className="flex items-center justify-center p-2"
          onClick={handleReset}
        >
          <IterationCw className="h-3 w-3 md:h-6 md:w-6" />
        </button>
      </div>

      <div className="grid h-[400px] w-[90vw] place-content-center md:h-[400px] md:w-full md:max-w-2xl">
        <Image
          className={`!pointer-events-auto cursor-move object-contain`}
          ref={imageRef}
          src={imageUrl}
          width={500}
          height={400}
          alt=""
          priority
          placeholder={placeholder}
          style={{
            transform: `scale(${scale}) translate(${position.x}px, ${position.y}px)`,
            cursor: 'move',
          }}
          draggable={false}
        />
      </div>
    </div>
  );
}

export default ImageZoomInOut;
