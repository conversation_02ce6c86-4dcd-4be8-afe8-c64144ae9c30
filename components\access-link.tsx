'use client';

import { useSession } from 'next-auth/react';
import Link from 'next/link';

function AccessLinks() {
  const { data: session, status } = useSession();
  if (status == 'loading') {
    return null;
  }
  if (status == 'unauthenticated') {
    return null;
  }

  if (status == 'authenticated' && session?.user.role == 'admin') {
    return (
      <Link
        href={'/dashboard/administration'}
        className="mx-auto text-[1.025rem] text-purple-400 underline-offset-2"
      >
        Administration
      </Link>
    );
  } else if (status == 'authenticated' && session?.user.role == 'dealer') {
    return (
      <Link
        href={'/dashboard/profiles'}
        className="mx-auto text-[1.025rem] text-purple-400 underline-offset-2"
      >
        Espace professionnel
      </Link>
    );
  } else if (status == 'authenticated' && session?.user.role == 'superdealer') {
    return (
      <Link
        href={'/dashboard/superdealer-groups'}
        className="mx-auto text-[1.025rem] text-purple-400 underline-offset-2"
      >
        Gestion des groupes
      </Link>
    );
  } else {
    return null;
  }
}

export default AccessLinks;
