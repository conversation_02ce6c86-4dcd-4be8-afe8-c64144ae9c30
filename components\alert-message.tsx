import { AlertCircle } from 'lucide-react';

import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { PropsWithChildren } from 'react';

interface AlertDestructiveProps {
  description: string;
}

export function AlertDestructive({
  description,
  children,
}: PropsWithChildren<AlertDestructiveProps>) {
  return (
    <Alert variant="destructive">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{description}</AlertDescription>
      <div className="my-2">{children}</div>
    </Alert>
  );
}
