'use client';
import Link from 'next/link';
import React, { useState, useTransition } from 'react';
import { Label } from '../ui/label';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Icons } from '../Icons';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';

import { ChevronsUpDown, Loader2 } from 'lucide-react';
import { toast } from '../ui/use-toast';
import axios, { AxiosError } from 'axios';
import { useForm, SubmitHandler, useFormState } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Checkbox } from '../ui/checkbox';
import { useCountries } from '@/hooks/useCountries';
import { VirtualizedCountryList } from '../ui/VirtualizedCountryList';

const verifEmail = async (email: string) => {
  try {
    const { data } = await axios.post(
      `https://abjectof-conoda2.onrender.com/api/user/check-email`,
      {
        email: email,
      },
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      },
    );

    return { data, error: false };
  } catch (error) {
    if (error instanceof AxiosError) {
      if (error.response?.status == 404) {
        return { data: error.response?.data, error: false };
      }
    }
    return { data: null, error };
  }
};
export default function SignUpForm({ callbackUrl }: { callbackUrl: string }) {
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [pending, startTransition] = useTransition();
  const reg = new RegExp(
    '^[a-zA-Z0-9]+(?:.[a-zA-Z0-9]+)*@[a-zA-Z0-9]+(?:.[a-zA-Z0-9]+)*$',
  );
  const SignUpSchema = z
    .object({
      email: z.string().email().regex(reg, { message: 'Email invalide' }),
      password: z.string().min(5, { message: 'Minimum 5 caracteres' }),
      Cpassword: z.string().min(5, { message: 'Minimum 5 caracteres' }),
      phone: z.string(),
    })
    .refine((data) => data.password === data.Cpassword, {
      message: 'Vos mots de passe ne correspondent pas',
      path: ['Cpassword'], // path of error
    });

  type SignUpInput = z.infer<typeof SignUpSchema>;
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [open, setOpen] = React.useState(false);
  const [value, setValue] = useState<string>('cameroon');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const router = useRouter();

  // Utilisation du hook pour le chargement lazy des pays
  const {
    countries,
    isLoading: countriesLoading,
    error: countriesError,
  } = useCountries(searchTerm);

  const createUser = async (payload: any) => {
    try {
      const res = await axios.post(
        `https://abjectof-conoda2.onrender.com/api/auth/register`,
        payload,
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        },
      );
      toast({
        description: 'Compte a créé avec succès!',
      });
      startTransition(() => {
        router.replace(`/signin?callbackUrl=${callbackUrl ?? '/'}`);
      });
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.code === 'ERR_BAD_REQUEST') {
          return toast({
            title: 'Impossible de créer votre compte',
            variant: 'destructive',
          });
        }
        if (
          error.code === 'ERR_NETWORK' ||
          error.code === 'ETIMEDOUT' ||
          error.code === 'ENOTFOUND'
        ) {
          return toast({
            title: 'Verifier votre connexion internet',
            variant: 'destructive',
          });
        }
      }
      return toast({
        title: 'Erreur inconnue',
        description: 'Veuillez réessayer',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };
  const { register, handleSubmit, formState, control } = useForm<SignUpInput>({
    defaultValues: {
      email: '',
      password: '',
      Cpassword: '',
      phone: '',
    },
    resolver: zodResolver(SignUpSchema),
    criteriaMode: 'all',
    mode: 'onBlur',
  });
  const { errors } = formState;
  const {} = useFormState({ control: control });
  const onSubmit: SubmitHandler<SignUpInput> = async ({
    email,
    password,
    phone,
  }) => {
    setIsLoading(true);
    const isEmailExist = await verifEmail(email);
    if (isEmailExist.error) {
      setIsLoading(false);
      return toast({
        title: 'Impossible de verifier votre Email',
        variant: 'destructive',
      });
    }
    if (isEmailExist.data.message == 'email exist') {
      setIsLoading(false);
      return toast({
        title: 'Cet Email est déjà utilisé',
        description: (
          <div>
            Veuillez changer <span className="text-sm font-bold">{email}</span>
          </div>
        ),
        variant: 'destructive',
      });
    }

    const pays = countries?.find(
      (pays) =>
        pays.name.common.toLocaleLowerCase() === value.toLocaleLowerCase(),
    );
    const payload: any = {
      email: email.trim(),
      password,
      phone: `${pays?.idd.root!}${pays?.idd?.suffixes?.at(0)!}${phone}`,
      role: 'client',
      remain: null,
      pays: pays?.name.common,
    };

    await createUser(payload);
    setIsLoading(false);
  };

  return (
    <>
      <div className="mx-auto flex w-full flex-col justify-center space-y-6">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-bold tracking-tight">
            Rejoignez plus de 25000 utilisateurs dans 25 pays au monde !
          </h1>
          <p className="text-md text-muted-foreground">
            Entrez les informations pour créer un compte.
          </p>
        </div>
        <div className={'grid gap-6'}>
          <form onSubmit={handleSubmit(onSubmit)} noValidate>
            <div className="grid gap-4">
              <div className={`grid gap-2`}>
                <Label className="sr-only" htmlFor="email">
                  Email
                </Label>
                <Input
                  id="email"
                  placeholder="Adresse mail"
                  type="email"
                  tabIndex={1}
                  className=""
                  autoCapitalize="none"
                  autoComplete="email"
                  autoCorrect="off"
                  disabled={isLoading}
                  {...register('email', {
                    required: {
                      value: true,
                      message: 'Ce champs est obligatoire',
                    },

                    validate: {
                      isValidEmail: (fieldValue) => {
                        const reg = new RegExp(
                          '^[a-zA-Z0-9]+(?:.[a-zA-Z0-9]+)*@[a-zA-Z0-9]+(?:.[a-zA-Z0-9]+)*$',
                        );
                        return (
                          reg.test(fieldValue) || "L'email n'est pas valide"
                        );
                      },
                      emailAvailable: async (fieldValue) => {
                        return true;
                      },
                    },
                  })}
                />
                {errors.email && (
                  <p className="my-1 text-sm text-red-500">
                    {errors.email.message}
                  </p>
                )}
                <Label className="sr-only" htmlFor="tel">
                  Téléphone
                </Label>
                <div className="flex gap-1">
                  <Popover open={open} onOpenChange={setOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        tabIndex={2}
                        variant="outline"
                        role="combobox"
                        aria-expanded={open}
                        className="w-[100px] justify-between"
                      >
                        {!countriesLoading && countries.length > 0 ? (
                          <>
                            {value
                              ? countries.find(
                                  (pays) =>
                                    pays.name.common.toLocaleLowerCase() ===
                                    value.toLocaleLowerCase(),
                                )?.idd.root! +
                                countries
                                  ?.find(
                                    (pays) =>
                                      pays?.name?.common.toLocaleLowerCase() ===
                                      value.toLocaleLowerCase(),
                                  )
                                  ?.idd?.suffixes?.at(0)!
                              : 'Pays'}
                          </>
                        ) : (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        )}
                        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent
                      side="top"
                      className="z-[100] max-h-[400px] w-[200px] p-0"
                    >
                      <div className="flex flex-col">
                        <div className="flex items-center border-b px-3">
                          <input
                            type="text"
                            placeholder="Rechercher un pays..."
                            className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                          />
                        </div>

                        {countriesError && (
                          <div className="p-4 text-center text-sm text-red-600">
                            Erreur de chargement des pays
                          </div>
                        )}

                        {countriesLoading ? (
                          <div className="flex h-20 items-center justify-center">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            <span className="ml-2 text-sm">
                              Chargement des pays...
                            </span>
                          </div>
                        ) : countries.length === 0 && searchTerm ? (
                          <div className="p-4 text-center text-sm text-muted-foreground">
                            Aucun pays trouvé pour "{searchTerm}"
                          </div>
                        ) : (
                          <VirtualizedCountryList
                            countries={countries}
                            selectedValue={value}
                            onSelect={(selectedValue) => {
                              setValue(
                                selectedValue === value.toLowerCase()
                                  ? ''
                                  : selectedValue,
                              );
                              setOpen(false);
                            }}
                            height={320}
                          />
                        )}
                      </div>
                    </PopoverContent>
                  </Popover>
                  <Input
                    id="tel"
                    placeholder="Numéro"
                    title={
                      value.length == 0
                        ? "Selectionnez d'abord votre pays"
                        : 'Numero de telephone'
                    }
                    type="number"
                    tabIndex={3}
                    autoCorrect="off"
                    disabled={isLoading || value.length == 0}
                    {...register('phone', {
                      required: {
                        value: true,
                        message: 'Ce champ est obligatoire',
                      },
                    })}
                  />
                </div>
                {errors.phone && (
                  <p className="my-1 text-sm text-red-500">
                    {errors.phone.message}
                  </p>
                )}

                <Label className="sr-only" htmlFor="pwd">
                  Mot de passe
                </Label>
                <Input
                  id="pwd"
                  tabIndex={4}
                  placeholder="Votre mot de passe"
                  type="password"
                  autoCapitalize="none"
                  autoCorrect="off"
                  disabled={isLoading}
                  {...register('password', {
                    required: {
                      value: true,
                      message: 'Ce champ est obligatoire',
                    },
                  })}
                />
                {errors.password && (
                  <p className="my-1 text-sm text-red-500">
                    {errors.password.message}
                  </p>
                )}
                <Label className="sr-only" htmlFor="cpwd">
                  Confirmation
                </Label>
                <Input
                  id="cpwd"
                  placeholder="Confirmer Votre mot de passe"
                  type="password"
                  tabIndex={5}
                  autoCapitalize="none"
                  autoCorrect="off"
                  disabled={isLoading}
                  {...register('Cpassword', {
                    required: {
                      value: true,
                      message: 'Ce champ est obligatoire',
                    },
                  })}
                />
                {errors.Cpassword && (
                  <p className="my-1 text-sm text-red-500">
                    {errors.Cpassword.message}
                  </p>
                )}
              </div>

              <div className="items-top my-2 flex space-x-2">
                <Checkbox
                  id="terms1"
                  checked={acceptTerms}
                  onCheckedChange={(checked: any) => setAcceptTerms(checked)}
                />
                <div className="grid gap-1.5 leading-none">
                  <label
                    htmlFor="terms1"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    Accepter les conditions.
                  </label>
                  <p className="text-sm text-muted-foreground">
                    En cochant cette case vous acceptez nos{' '}
                    <Link
                      href={'/politique-confidentialite'} // précédemment => href="/terms"
                      className="underline underline-offset-4 hover:text-primary"
                    >
                      conditions d&apos;utilisation
                    </Link>{' '}
                    et notre{' '}
                    <Link
                      href={'/politique-confidentialite'} // précédemment =>    href="/privacy"
                      className="underline underline-offset-4 hover:text-primary"
                    >
                      politique de confidentialité
                    </Link>
                    .
                  </p>
                </div>
              </div>
              <Button
                tabIndex={6}
                disabled={isLoading || !acceptTerms}
                type="submit"
                className={`bg-blue-500`}
              >
                {isLoading && (
                  <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                )}
                Créer mon compte
              </Button>
            </div>
          </form>
          {/* <DevTool control={control}/> */}
        </div>
        <p className="flex px-8 text-center text-sm text-muted-foreground">
          Vous avez déjà un compte?
          <Link
            href={'/signin'}
            replace
            className="text-md pl-0.5 text-blue-500 underline underline-offset-4"
          >
            Se connecter
          </Link>
        </p>
      </div>
      {/* )} */}
    </>
  );
}
