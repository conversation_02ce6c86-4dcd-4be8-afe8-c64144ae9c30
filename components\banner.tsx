import { TEST_URL_ON_TEST } from '@/config';
import { useTestState } from '@/context/test';
import { ArrowRightIcon } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React from 'react';
type BannerProps = {
  url: string;
  message: string;
};
function Banner({ url, message }: BannerProps) {
  const path = usePathname()!;
  const { isTart } = useTestState();
  return (
    <Link
      href={url}
      target="_blank"
      className={`group w-full max-w-[100vw] grid-cols-[auto_1fr] items-start justify-center gap-1 bg-blue-500 px-4 py-2 text-white md:items-center ${
        path.includes('signin') ||
        path.includes('signup') ||
        TEST_URL_ON_TEST.test(path) ||
        (path.includes('test') && isTart)
          ? 'hidden'
          : 'grid md:flex'
      }`}
    >
      <span>✨ </span>
      <span className="max-w-[50ch] break-words font-sans text-xs font-bold md:max-w-none md:text-center md:text-sm">
        {message}
      </span>
      <ArrowRightIcon className="ml-1 hidden w-4 transition-all group-hover:-rotate-45 md:block" />
    </Link>
  );
}

export default Banner;
