import { useState } from 'react';
import copy from 'copy-to-clipboard';
import { Check, Copy } from 'lucide-react';

interface CopyToClipboardButtonProps {
  text: string;
  className?: string;
}

const CopyToClipboardButton = ({
  text,
  className,
}: CopyToClipboardButtonProps) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    copy(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <button
      onClick={handleCopy}
      className={className ?? 'text-blue-600 hover:underline'}
      aria-label="Copier dans le presse-papiers"
    >
      {copied ? (
        <Check className="h-5 w-5 text-green-500" />
      ) : (
        <Copy className="h-5 w-5" />
      )}
    </button>
  );
};

export default CopyToClipboardButton;
