'use client';
import { useEEState } from '@/context/ee';
import { cn } from '@/lib/utils';
import wordCount from '@/lib/word-count';
import { useEditor, EditorContent, Editor } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import { useEffect, useState } from 'react';
import { Button } from './ui/button';
interface Props {
  num: number;
  text: string;
}
function CustomEditor({ num, text }: Props) {
  const { setTextOne, setTextTwo, setTextThree } = useEEState();
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        hardBreak: {
          HTMLAttributes: <br />,
        },
      }),
    ],
    content: text,
    editorProps: {
      attributes: {
        class:
          ' bg-white/10 backdrop-blur-[2px] text-base min-h-[450px]   w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
      },
    },
    onUpdate({ editor }) {
      setPureText(editor.getText());
      switch (num) {
        case 1:
          setTextOne(editor.getHTML());
          break;
        case 2:
          setTextTwo(editor.getHTML());
          break;
        case 3:
          setTextThree(editor.getHTML());
          break;
        default:
          break;
      }
    },
  });
  const [pureText, setPureText] = useState<string>('');
  useEffect(() => {
    if (editor) {
      setPureText(editor.getText());
    }
  }, [editor]);
  if (!editor) {
    return null;
  }
  return (
    <div className="flex flex-col gap-4 lg:flex-row">
      <div className="bgc flex w-full flex-col gap-1.5 md:w-[60%]">
        <EditorContent
          autoComplete="off"
          autoCorrect="off"
          spellCheck={false}
          editor={editor}
        />
        <div className="flex items-center space-x-3">
          <span className="flex items-center gap-1 text-gray-400">
            Nombre de mots:{' '}
            <p className="font-semibold text-black">{wordCount(pureText)}</p>
          </span>
        </div>
      </div>

      <div className="w-full lg:w-[30%]">
        <SpecialKeyBoard editor={editor} />
      </div>
    </div>
  );
}

export default CustomEditor;

const SpecialKeyBoard = ({ editor }: { editor: Editor | null }) => {
  const keys = [
    'é',
    'è',
    'ê',
    'à',
    'ù',
    'û',
    'ô',
    'ç',
    'œ',
    'ï',
    'ë',
    'â',
    'î',
    'û',
    '!',
    '?',
    '.',
    ',',
    ';',
    ':',
    '(',
    ')',
    "'",
    '"',
    '-',
    ' ',
  ] as const;

  const adText = (insert: string) => {
    if (editor) {
      editor.commands.focus();
      const pos = editor.view.state.selection.$anchor.pos;
      editor.chain().focus().insertContentAt(pos, insert).run();
    }
  };
  return (
    <div className="mt-3 grid grid-cols-7 gap-1 md:h-20">
      {keys.map((key) => (
        <Button
          variant={'outline'}
          size={'sm'}
          key={key}
          onClick={() => {
            adText(key);
          }}
          className={cn('rounded-md p-2', key === ' ' ? 'col-span-2' : '')}
        >
          {key}
        </Button>
      ))}
    </div>
  );
};
