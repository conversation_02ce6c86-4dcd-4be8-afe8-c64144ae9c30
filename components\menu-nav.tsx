/* BOUTON A PROPOS /MENU */
'use client';

import * as React from 'react';
import Link from 'next/link';

import { cn } from '@/lib/utils';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from '@/components/ui/navigation-menu';
import { usei18nState } from '@/context/i18n';
import { useTranslation } from '@/app/i18n/client';

const components: { title: string; href: string; description: string }[] = [
  {
    title: "C'est quoi le TCF",
    href: '/about#whattcf',
    description: '',
  },
  {
    title: 'Les épreuves au TCF',
    href: '/about#epreuves',
    description: '',
  },
  {
    title: 'Inscription au TCF',
    href: '/about#registration',
    description: '',
  },
  {
    title: 'Résultat du TCF',
    href: '/about#resultats',
    description: '',
  },
  {
    title: 'Liens utiles',
    href: '/about#links',
    description: '',
  },
  {
    title: 'AUTRES',
    href: '/about',
    description: '',
  },
];

export function NavigationMenuDemo() {
  const { lng } = usei18nState();
  const { t } = useTranslation(lng, 'navbar');
  return (
    <NavigationMenu>
      <NavigationMenuList>
        <NavigationMenuItem>
          <Link href="/" legacyBehavior passHref>
            <NavigationMenuLink className={navigationMenuTriggerStyle()}>
              {t('title', { ns: '' })}
            </NavigationMenuLink>
          </Link>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuTrigger>{t('news')}</NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
              <li className="bga row-span-3">
                <NavigationMenuLink asChild>
                  <a
                    className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/10 to-muted/60 p-6 no-underline outline-none focus:shadow-md"
                    href="/"
                  >
                    {/* <Icons.logo className="h-6 w-6" />
                    <div className="mb-2 mt-4 text-lg font-medium">
                      Objectif Canada
                    </div> */}
                    <p className="text-sm leading-tight text-muted-foreground"></p>
                  </a>
                </NavigationMenuLink>
              </li>
              <ListItem href="/news/tcf" title={`${t('news')} TCF`}>
                Découvrez Les nouveaux sujets.
              </ListItem>
              <ListItem href="/news/tef" title={`${t('news')} TEF`}>
                Découvrez Les nouveaux sujets.
              </ListItem>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuTrigger>{t('A Propos du TCF')}</NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
              {components.map((component) => (
                <ListItem
                  key={component.title}
                  title={component.title}
                  href={component.href}
                >
                  {component.description}
                </ListItem>
              ))}
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuTrigger>Pages</NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
              <li className="bga row-span-3">
                <NavigationMenuLink asChild>
                  <a
                    className="flex h-full w-full select-none flex-col justify-end rounded-md bg-gradient-to-b from-muted/10 to-muted/60 p-6 no-underline outline-none focus:shadow-md"
                    href="/"
                  >
                    {/* <Icons.logo className="h-6 w-6" />
                    <div className="mb-2 mt-4 text-lg font-medium">
                      Objectif Canada
                    </div> */}
                    <p className="text-sm leading-tight text-muted-foreground"></p>
                  </a>
                </NavigationMenuLink>
              </li>
              <ListItem href="/faq" title="FAQ">
                Nous répondons à toutes vos questions.
              </ListItem>
              <ListItem
                href="/politique-confidentialite"
                title={t('Politique de confidentialite')}
              >
                Découvrez comment nous gérons vos informations personnelles.
              </ListItem>
              <ListItem
                href="/condition-remboursement"
                title={t('Condition de remboursement')}
              >
                Découvrez comment et dans quelles mesures vous pouvez être
                rembourser.
              </ListItem>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>

        <NavigationMenuItem>
          <Link href="/contact" legacyBehavior passHref>
            <NavigationMenuLink className={navigationMenuTriggerStyle()}>
              Contact
            </NavigationMenuLink>
          </Link>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  );
}

const ListItem = React.forwardRef<
  React.ElementRef<'a'>,
  React.ComponentPropsWithoutRef<'a'>
>(({ className, title, children, ...props }, ref) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <a
          ref={ref}
          className={cn(
            'block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground',
            className,
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="line-clamp-2 text-sm leading-snug text-muted-foreground">
            {children}
          </p>
        </a>
      </NavigationMenuLink>
    </li>
  );
});
ListItem.displayName = 'ListItem';
