import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { PropsWithChildren } from 'react';
import { ScrollArea } from './ui/scroll-area';

type DialogContainerProps = PropsWithChildren<{
  content?: React.ReactNode;
  title: string;
  description: string;
  srOnly?: boolean;
}>;

export function DialogContainer({
  content,
  children,
  title,
  description,
  srOnly = false,
}: DialogContainerProps) {
  return (
    <Dialog>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-w[80vw] mx-auto sm:max-w-[425px]">
        <ScrollArea className="max-h-[30rem]">
          <DialogHeader>
            <DialogTitle className={`${srOnly ? 'sr-only' : ''}`}>
              {title}
            </DialogTitle>
            <DialogDescription className={`${srOnly ? 'sr-only' : ''}`}>
              {description}
            </DialogDescription>
          </DialogHeader>
          {content}
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
