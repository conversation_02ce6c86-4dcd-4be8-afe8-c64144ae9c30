import { <PERSON><PERSON><PERSON><PERSON>, EditorContext, useEditor } from '@tiptap/react';
import { StarterKit } from '@tiptap/starter-kit';
import { Underline } from '@tiptap/extension-underline';
// import { Superscript } from '@tiptap/extension-superscript';
// import { Subscript } from '@tiptap/extension-subscript';
import { MarkButton } from '@/components/tiptap-ui/mark-button';
import { UndoRedoButton } from '@/components/tiptap-ui/undo-redo-button';

import '@/components/tiptap-node/code-block-node/code-block-node.scss';
import '@/components/tiptap-node/paragraph-node/paragraph-node.scss';
import { NodeButton } from '@/components/tiptap-ui/node-button';
interface TipTapEditorProps {
  content: string;
  onChange: (content: string) => void;
}
export default function TipTapEditor({ content, onChange }: TipTapEditorProps) {
  const editor = useEditor({
    editorProps: {
      attributes: {
        class:
          'prose prose-sm max-w-none focus:outline-none overflow-y-auto max-h-[400px]',
      },
    },
    immediatelyRender: false,
    extensions: [StarterKit, Underline],
    content: content,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
  });

  return (
    <EditorContext.Provider value={{ editor }}>
      <div className="relative flex flex-col gap-4">
        <div className="sticky top-1 z-10 flex items-center gap-2 border-b border-gray-200 bg-white pb-2">
          <UndoRedoButton action="undo" />
          <UndoRedoButton action="redo" />
          <MarkButton type="bold" />
          <MarkButton type="italic" />
          <MarkButton type="strike" />
          <MarkButton type="underline" />
          <NodeButton type="blockquote" />
        </div>

        <EditorContent editor={editor} role="presentation" />
      </div>
    </EditorContext.Provider>
  );
}
