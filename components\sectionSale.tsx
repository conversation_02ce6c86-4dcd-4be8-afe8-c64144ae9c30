'use client';

import Link from 'next/link';
import { buttonVariants } from '@/components/ui/button';

export default function SectionSale() {
  return (
    <div className="pb-16" style={{ fontFamily: '"Lato", sans-serif' }}>
      <section className="md:max-w-8xl bg-white pt-16 md:mx-auto">
        <div>
          <div className="relative isolate">
            <div
              aria-hidden="true"
              className="pointer-events-none absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80"
            >
              <div
                style={{
                  clipPath:
                    'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
                }}
                className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
              />
            </div>
          </div>
          <div role="contentinfo" className="flex flex-col items-center">
            <p
              tabIndex={0}
              className="text-center text-sm uppercase leading-4 text-gray-600 focus:outline-none"
            >
              en quelques étapes
            </p>
            <h1
              tabIndex={0}
              className="pt-4 text-center text-2xl font-extrabold leading-10 text-gray-800 focus:outline-none lg:text-4xl"
            >
              Payez un abonnement et profitez d&apos;un accès total à toutes les
              séries du parc.
            </h1>
            <Link
              className={buttonVariants({
                size: 'lg',
                className:
                  'mx-auto mt-5 text-lg font-medium tracking-wider no-underline',
              })}
              href={'/checkout'}
            >
              Voir les offres!
            </Link>
          </div>
          <div
            tabIndex={0}
            aria-label="group of cards"
            className="mt-20 flex flex-wrap justify-center gap-10 focus:outline-none"
          >
            <div
              tabIndex={0}
              aria-label="card 1"
              className="flex pb-20 focus:outline-none sm:w-full md:w-5/12"
            >
              <div className="relative mr-5 h-20 w-20">
                <div className="absolute right-0 top-0 mr-1 mt-2 h-16 w-16 rounded bg-indigo-100" />
                <div className="absolute bottom-0 left-0 mr-3 mt-2 flex h-16 w-16 items-center justify-center rounded bg-indigo-700 text-white">
                  <img
                    src="https://tuk-cdn.s3.amazonaws.com/can-uploader/icon_and_text-SVG1.svg"
                    alt="drawer"
                  />
                </div>
              </div>
              <div className="w-10/12">
                <h2
                  tabIndex={0}
                  className="text-lg font-bold leading-tight text-gray-800 focus:outline-none"
                >{`Test intégral en conditions réelles 
avec correction`}</h2>
                <p
                  tabIndex={0}
                  className="pt-2 text-base leading-normal text-gray-600 focus:outline-none"
                >{`Nous mettons à votre disposition 
l’intégralité des tests de Compréhension 
orale, Compréhension écrite, Expression 
Orale, Expression écrite.`}</p>
              </div>
            </div>
            <div
              tabIndex={0}
              aria-label="card 2"
              className="flex pb-20 focus:outline-none sm:w-full md:w-5/12"
            >
              <div className="relative mr-5 h-20 w-20">
                <div className="absolute right-0 top-0 mr-1 mt-2 h-16 w-16 rounded bg-indigo-100" />
                <div className="absolute bottom-0 left-0 mr-3 mt-2 flex h-16 w-16 items-center justify-center rounded bg-indigo-700 text-white">
                  <img
                    src="https://tuk-cdn.s3.amazonaws.com/can-uploader/icon_and_text-SVG2.svg"
                    alt="check"
                  />
                </div>
              </div>
              <div className="w-10/12">
                <h2
                  tabIndex={0}
                  className="text-lg font-semibold leading-tight text-gray-800 focus:outline-none"
                >{`Un suivi efficace dans votre 
préparation`}</h2>
                <p
                  tabIndex={0}
                  className="pt-2 text-base leading-normal text-gray-600 focus:outline-none"
                >{`Nous mettons à votre disposition 
l’intégralité des tests avec des corrections 
proposés par des professionnels des IFC `}</p>
              </div>
            </div>
            <div
              tabIndex={0}
              aria-label="card 3"
              className="flex pb-20 focus:outline-none sm:w-full md:w-5/12"
            >
              <div className="relative mr-5 h-20 w-20">
                <div className="absolute right-0 top-0 mr-1 mt-2 h-16 w-16 rounded bg-indigo-100" />
                <div className="absolute bottom-0 left-0 mr-3 mt-2 flex h-16 w-16 items-center justify-center rounded bg-indigo-700 text-white">
                  <img
                    src="https://tuk-cdn.s3.amazonaws.com/can-uploader/icon_and_text-SVG3.svg"
                    alt="html tag"
                  />
                </div>
              </div>
              <div className="w-10/12">
                <h2
                  tabIndex={0}
                  className="text-lg font-semibold leading-tight text-gray-800 focus:outline-none"
                >{`Test d’Expression écrite en ligne `}</h2>
                <p
                  tabIndex={0}
                  className="pt-2 text-base leading-normal text-gray-600 focus:outline-none"
                >{`Nous mettons à votre disposition 
l’intégralité des tests d’Expression écrite 
en conditions réelles d’examen avec des 
corrections  rapides par nos experts venus 
de l’IFC`}</p>
              </div>
            </div>
            <div
              tabIndex={0}
              aria-label="card 4"
              className="flex pb-20 focus:outline-none sm:w-full md:w-5/12"
            >
              <div className="relative mr-5 h-20 w-20">
                <div className="absolute right-0 top-0 mr-1 mt-2 h-16 w-16 rounded bg-indigo-100" />
                <div className="absolute bottom-0 left-0 mr-3 mt-2 flex h-16 w-16 items-center justify-center rounded bg-indigo-700 text-white">
                  <img
                    src="https://tuk-cdn.s3.amazonaws.com/can-uploader/icon_and_text-SVG4.svg"
                    alt="monitor"
                  />
                </div>
              </div>
              <div className="w-10/12">
                <h2
                  tabIndex={0}
                  className="text-lg font-semibold leading-tight text-gray-800 focus:outline-none"
                >{`Test d’Expression orale en ligne `}</h2>
                <p
                  tabIndex={0}
                  className="pt-2 text-base leading-normal text-gray-600 focus:outline-none"
                >{`Nous mettons à votre disposition l’intégralité 
des tests d’Expression orale en conditions 
réelles d’examen avec des corrections  rapides 
par nos experts venus de l’IFC`}</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
