import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
function ShinnyButton({
  text,
  className,
}: {
  text: string;
  className?: string;
}) {
  return (
    <motion.button
      className="radial-gradient relative mx-auto rounded-md px-6 py-2"
      initial={{ '--x': '100%', scale: 1 } as any}
      animate={{ '--x': '-100%' } as any}
      whileTap={{ scale: 0.97 }}
      transition={{
        repeat: Infinity,
        repeatType: 'loop',
        repeatDelay: 1,
        type: 'spring',
        stiffness: 20,
        damping: 15,
        mass: 2,
        scale: {
          type: 'spring',
          stiffness: 10,
          damping: 5,
          mass: 0.1,
        },
      }}
    >
      <span
        className={cn(
          'linear-mask relative block h-full w-full font-medium tracking-wide text-neutral-100',
          className,
        )}
      >
        {text}
      </span>
      <span className="linear-overlay absolute inset-0 block rounded-md p-px" />
    </motion.button>
  );
}

export default ShinnyButton;
