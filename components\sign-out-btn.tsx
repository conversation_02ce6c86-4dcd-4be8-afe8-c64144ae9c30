import { Button } from '@/components/ui/button';
import { signOut, useSession } from 'next-auth/react';
import { usePath } from '@/hooks/use-path';

export function SignOutBtn({
  handleClick,
}: {
  handleClick?: () => void;
}): JSX.Element {
  const { data: session } = useSession();
  const pathL = usePath();

  return (
    <>
      {session ? (
        <Button
          className="mt-4 w-full"
          variant={'destructive'}
          onClick={async () => {
            await signOut({
              redirect: true,
              callbackUrl: `/signin?callbackUrl=${pathL}`,
            });
            if (handleClick) handleClick();
          }}
        >
          Se déconnecter
        </Button>
      ) : null}
    </>
  );
}
