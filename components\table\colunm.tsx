'use client';

import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { DataTableColumnHeader } from './data-table-column-header';
import { FillueilRow, Row } from '@/types';
import format from 'date-fns/format';

export const columns: ColumnDef<Row>[] = [
  {
    accessorKey: 'id',
    header: ({ column }) => column.toggleVisibility(false),
  },
  {
    accessorKey: 'isEEview',
    header: ({ column }) => column.toggleVisibility(false),
  },
  {
    accessorKey: 'isEOview',
    header: ({ column }) => column.toggleVisibility(false),
  },
  {
    accessorKey: 'time',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Horodateur" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {format(new Date(row.getValue('time')), "dd/MM/yyyy' 'HH:mm:ss")}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'serie',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Serie" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue('serie')}
          </span>
        </div>
      );
    },
    enableGrouping: true,
  },
  {
    accessorKey: 'CE',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="CE" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue('CE')}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'CO',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="CO" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue('CO')}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'EO',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="EO" />
    ),
    cell: ({ row }) => {
      const isView = row.getValue('isEOview') as boolean;
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue('EO')}
          </span>
          {row.getValue('EO') !== '-' && !isView ? <Badge>new</Badge> : null}
        </div>
      );
    },
  },
  {
    accessorKey: 'EE',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="EE" />
    ),
    cell: ({ row }) => {
      const isView = row.getValue('isEEview') as boolean;
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue('EE')}
          </span>
          {row.getValue('EE') !== '-' && !isView ? <Badge>new</Badge> : null}
        </div>
      );
    },
  },
  {
    accessorKey: 'MOY',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Moyenne" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue('MOY')}
          </span>
        </div>
      );
    },
  },
];

export const columnsF: ColumnDef<FillueilRow>[] = [
  {
    accessorKey: 'email',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Filleuls" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue('email')}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'last',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Dernière connexion" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue('last')}
          </span>
        </div>
      );
    },
  },
  {
    accessorKey: 'end',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Fin Abonnement" />
    ),
    cell: ({ row }) => {
      return (
        <div className="flex space-x-2">
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue('end')}
          </span>
        </div>
      );
    },
  },
];
