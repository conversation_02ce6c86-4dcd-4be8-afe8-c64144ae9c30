'use client';
import Link from 'next/link';
import React from 'react';
import { cn, getNiveau } from '@/lib/utils';
import { Resultat } from '@/types';
import format from 'date-fns/format';
import { buttonVariants } from '@/components/ui/button';
interface Props {
  detailSet: Omit<Resultat, 'serie'> & { serie: SerieTEF };
  type: string;
}

const Socre = ({ score, type }: { score: number; type: string }) => {
  return (
    <div className="flex min-w-[110px] flex-nowrap items-center justify-center gap-1 font-bold text-gray-600 md:min-w-[110px]">
      <p className="">{type}</p>
      <span className="flex">
        <p
          className={cn('', {
            'text-red-500': score < 450,
            'text-green-400': score >= 450,
          })}
        >
          {score}
        </p>
        /699
      </span>
      <p
        className={cn('rounded-sm border bg-zinc-100 px-3 py-2', {
          'text-red-500': score < 450,
          'text-green-400': score >= 450,
        })}
      >
        {score !== null ? getNiveau(score) : ''}
      </p>
    </div>
  );
};

export default function ComprehensionResultHeaderTEF({
  detailSet,
  type,
}: Props) {
  return (
    <>
      <div className="w-ful flex h-14 items-center gap-4 overflow-x-scroll rounded-md border px-2 md:overflow-hidden">
        <Link
          href={`/examen/tef/${detailSet.serie.libelle}`}
          className={buttonVariants({
            variant: 'default',
            class: 'min-w-[150px] md:min-w-[200px]',
          })}
        >
          Refaire ce test
        </Link>
        <Socre score={detailSet.resultat} type={type} />
        <p className="min-w-[405px]">
          Effectué le{' '}
          {format(new Date(detailSet.createdAt), "dd/MM/yyyy' à 'HH:mm:ss")}
        </p>
      </div>
    </>
  );
}
