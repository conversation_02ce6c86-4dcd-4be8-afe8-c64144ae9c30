'use client';
import React, { memo, useMemo } from 'react';
import 'react-sweet-progress/lib/style.css';
import { Resultat, Resultset } from '@/types';
import QuestionComponentTEF from './question';
import QuestionComponentTEFCO from './questionCO';
const MemorizedQuestion = memo(QuestionComponentTEF);
const MemorizedQuestionCO = memo(QuestionComponentTEFCO);
export function DisplayQuestionTEFCE({
  detailset,
}: {
  detailset: Omit<Resultat, 'serie'> & { serie: SerieTEF };
}) {
  const resulSet = JSON.parse(detailset.payload) as Resultset[];
  //   console.log(resulSet);
  //   console.log(detailset.payload);

  const memoquestion = useMemo(() => {
    return detailset.serie.ceQuestions.questions
      .sort((a, b) => {
        return a.numero - b.numero;
      })
      .filter((q) => resulSet.find((r) => r.questionId == q.numero))
      .map((q) => (
        <MemorizedQuestion
          serieLib={detailset.serie.libelle}
          question={q}
          resulSet={resulSet}
          key={q.numero}
        />
      ));
  }, [detailset, resulSet]);
  return (
    <>
      <div className="mb-4 w-full space-y-5 md:px-3">{memoquestion}</div>
    </>
  );
}

export function DisplayQuestionTEFCO({
  detailset,
}: {
  detailset: Omit<Resultat, 'serie'> & { serie: SerieTEF };
}) {
  const resulSet = JSON.parse(detailset.payload) as Resultset[];
  const memoquestion = useMemo(() => {
    return detailset.serie.coQuestions.questions
      .sort((a, b) => {
        return a.numero - b.numero;
      })
      .filter((q) => resulSet.find((r) => r.questionId == q.numero))
      .map((q) => (
        <MemorizedQuestionCO
          serieLib={detailset.serie.libelle}
          question={q}
          resulSet={resulSet}
          key={q.numero}
        />
      ));
  }, [detailset, resulSet]);
  return (
    <>
      <div className="mb-4 w-full space-y-5 md:px-3">{memoquestion}</div>
    </>
  );
}
