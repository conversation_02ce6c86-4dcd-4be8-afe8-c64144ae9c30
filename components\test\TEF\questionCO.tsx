'use client';
import { getPointTEF } from '@/lib/utils';
import { Resultset } from '@/types';
import Image from 'next/image';
import Link from 'next/link';
import React, { useState } from 'react';
import { BUCKET_BASE_URL } from '@/config';
import { Eye, X } from 'lucide-react';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import left from '@/public/left.png';
import logo from '@/public/logo4.png';
import ImageZoomInOut from '@/components/ZoumInOutImage';
import { Button } from '@/components/ui/button';

export default function QuestionComponentTEFCO({
  question,
  resulSet,
  serieLib,
}: {
  serieLib: string;
  question: Question;
  resulSet: Resultset[];
}) {
  const [showPalayer, setShowPlayer] = useState(false);
  const libelles = question.libelles.filter((l) => l !== null);
  const libelleImg = libelles.find((l) => l?.typeLibelle === 'image');
  const libelleAudio = libelles.find((l) => l?.typeLibelle === 'audio');

  return (
    <>
      <div className="flex h-fit w-full flex-col items-center gap-5 rounded-md p-3 md:flex-row">
        <div
          className={`group relative mb-6 w-full md:h-96 ${
            libelleImg ? 'h-96' : 'h-fit'
          } box-border flex flex-col items-center justify-center overflow-y-auto rounded-lg px-2 py-1 shadow-lg md:p-3`}
        >
          <>
            <Image
              alt="image-question"
              src={
                libelleImg ? `${BUCKET_BASE_URL}/${libelleImg.libelle}` : logo
              }
              fill
              priority
              className={'object-contain'}
            />
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant={'secondary'}
                  size={'icon'}
                  className="absolute left-2 top-2"
                >
                  <Eye className="text-blue-400 hover:text-blue-600" />
                </Button>
              </DialogTrigger>
              <DialogContent className="h-96 w-full max-w-xl md:max-w-2xl">
                <ImageZoomInOut
                  placeholder="empty"
                  imageUrl={
                    libelleImg
                      ? `${BUCKET_BASE_URL}/${libelleImg.libelle}`
                      : '/logo4.png'
                  }
                />
              </DialogContent>
            </Dialog>
          </>
        </div>
        <div className="relative flex w-full flex-col gap-4 overflow-hidden rounded-md p-1 text-sm md:mt-0 md:min-h-[200px] md:p-4">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              <span
                className={`${
                  'bg-blue-500'
                  //   userSuggestion?.isCorrect ? "bg-blue-500" : "bg-red-500"
                } flex h-10 w-16 items-center justify-center rounded-md p-4 text-base font-bold text-white md:text-lg`}
              >
                {question?.numero}
              </span>
              <span
                className={`flex h-10 items-center justify-center rounded-md bg-yellow-100 p-4 text-base font-bold md:text-lg`}
              >
                Serie {serieLib}
              </span>
            </div>
            <Link href={'#'} className="text-blue-500 underline">
              Aide ?
            </Link>
          </div>

          <>
            {showPalayer ? (
              <div className="relative !w-full">
                <audio
                  controls
                  src={`${BUCKET_BASE_URL}/${libelleAudio?.libelle}`}
                />
                <Button
                  variant={'outline'}
                  size={'icon'}
                  onClick={() => setShowPlayer(false)}
                  className="absolute -top-6 right-0 rounded-full border-2 !border-dashed p-1"
                >
                  <X className="h-4 w-4 text-red-500" />
                </Button>
              </div>
            ) : (
              <Button
                variant={'outline'}
                onClick={() => setShowPlayer(true)}
                className="mx-auto w-fit"
              >
                <span className="text-blue-500">Écouter la consigne</span>
              </Button>
            )}

            {question.consignes.length > 1
              ? resulSet
                  .filter((q) => q.questionId == question.numero)
                  .map((userSugestionId) => {
                    const consigne = question.consignes.find(
                      (consigne) => consigne._id == userSugestionId.consigneId,
                    );
                    const userSuggestion = consigne?.suggestions.find(
                      (s) => s._id == userSugestionId?.resId,
                    );
                    const goodSuggestion = consigne?.suggestions.find(
                      (s) => s.isCorrect == true,
                    );

                    return (
                      <div key={consigne?._id} className="grid gap-1">
                        <p
                          className={`text-2xl font-bold ${
                            userSuggestion?.isCorrect
                              ? 'text-emerald-500'
                              : 'text-red-500'
                          }`}
                        >
                          +
                          {userSuggestion?.isCorrect
                            ? getPointTEF(question.numero)
                            : 0}
                        </p>
                        <div className="flex flex-col gap-1 md:flex-row md:items-start">
                          <p className="">Votre réponse :</p>
                          <p
                            className={`max-w-[30ch] font-bold ${
                              userSuggestion?.isCorrect
                                ? 'text-emerald-500'
                                : 'text-red-500'
                            } `}
                          >
                            {userSuggestion?.text}
                          </p>
                        </div>
                        <div className="flex flex-col gap-1 md:flex-row md:items-start">
                          <p className="">La réponse juste :</p>
                          <p className={`max-w-[30ch] font-bold`}>
                            {goodSuggestion?.text}
                          </p>
                        </div>
                      </div>
                    );
                  })
              : resulSet
                  .filter((q) => q.questionId == question.numero)
                  .map((userSugestionId) => {
                    const consigne = question.consignes[0];
                    const userSuggestion = consigne?.suggestions.find(
                      (s) => s._id == userSugestionId?.resId,
                    );
                    const goodSuggestion = consigne?.suggestions.find(
                      (s) => s.isCorrect == true,
                    );

                    return (
                      <div key={consigne?._id} className="grid gap-1">
                        <p
                          className={`text-2xl font-bold ${
                            userSuggestion?.isCorrect
                              ? 'text-emerald-500'
                              : 'text-red-500'
                          }`}
                        >
                          +
                          {userSuggestion?.isCorrect
                            ? getPointTEF(question.numero)
                            : 0}
                        </p>
                        <div className="flex flex-col gap-1 md:flex-row md:items-center">
                          <p className="">Votre réponse :</p>
                          <p
                            className={`max-w-[30ch] font-bold ${
                              userSuggestion?.isCorrect
                                ? 'text-emerald-500'
                                : 'text-red-500'
                            } `}
                          >
                            {userSuggestion?.text}
                          </p>
                        </div>
                        <div className="flex flex-col gap-1 md:flex-row md:items-center">
                          <p className="">La réponse juste :</p>
                          <p className={`max-w-[30ch] font-bold`}>
                            {goodSuggestion?.text}
                          </p>
                        </div>
                      </div>
                    );
                  })}
          </>

          <Image
            alt="logo"
            width={70}
            height={70}
            src={left}
            loading="lazy"
            className="absolute bottom-2 right-4"
          />
        </div>
      </div>
    </>
  );
}

interface QuestionWithHoleProps {
  libelle: string;
}

const QuestionWithOneHole = ({ libelle }: QuestionWithHoleProps) => {
  const firstPart = libelle.split('<#>1<#>')[0];
  const secondPart = libelle.split('<#>1<#>')[1];

  return (
    <div className="inline-block h-fit">
      <span>{firstPart.replace(/\…{2,}/g, ' ')}</span>
      <span>____</span>
      <span>{secondPart.replace(/\…{2,}/g, ' ')}</span>
    </div>
  );
};

const QuestionWithTwoHole = ({ libelle }: QuestionWithHoleProps) => {
  const firstPart = libelle.split('<#>1<#>')[0];
  const inter = libelle.split('<#>1<#>')[1];
  const secondPart = inter.split('<#>2<#>')[0];
  const thirdPart = libelle.split('<#>2<#>')[1];

  return (
    <div className="inline-block h-fit text-justify">
      <span>{firstPart.replace(/\…{2,}/g, ' ')}</span>
      <span>____</span>
      <span>{secondPart.replace(/\…{2,}/g, ' ')}</span>
      <span>____</span>
      <span>{thirdPart.replace(/\…{2,}/g, ' ')}</span>
    </div>
  );
};
