'use client';
import { usePathname } from 'next/navigation';
import NextTopLoader from 'nextjs-toploader';
import React, { Suspense } from 'react';

function TopLoader() {
  const path = usePathname();
  return (
    <Suspense>
      <NextTopLoader
        // color={`${
        //   TEST_URL_ON_TEST.test(path) ? "hsl(var(--primary))" : "#ffff"
        // }`}
        color="red"
        showSpinner={false}
      />
    </Suspense>
  );
}

export default TopLoader;
