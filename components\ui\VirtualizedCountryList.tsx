import React, { useRef } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import { Country } from '@/types';

interface VirtualizedCountryListProps {
  countries: Country[];
  selectedValue: string;
  onSelect: (value: string) => void;
  height?: number;
}

export const VirtualizedCountryList: React.FC<VirtualizedCountryListProps> = ({
  countries,
  selectedValue,
  onSelect,
  height = 320, // 80 * 4 items par défaut
}) => {
  const parentRef = useRef<HTMLDivElement>(null);

  const virtualizer = useVirtualizer({
    count: countries.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 32, // Hauteur estimée de chaque item (12 * 4 = 48px)
    overscan: 10, // Augmenté pour une meilleure recherche
    measureElement: (element) => element?.getBoundingClientRect().height ?? 32,
  });

  if (countries.length === 0) {
    return (
      <div className="flex h-20 items-center justify-center text-sm text-muted-foreground">
        Aucun pays trouvé
      </div>
    );
  }

  return (
    <div
      ref={parentRef}
      className="overflow-auto"
      style={{
        height: `${height}px`,
      }}
    >
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => {
          const country = countries[virtualItem.index];
          const isSelected =
            selectedValue.toLowerCase() === country.name.common.toLowerCase();

          return (
            <div
              key={virtualItem.key as any}
              className={cn('absolute left-0 top-0 w-full', {
                'bg-zinc-300': isSelected,
              })}
              style={{
                height: `${virtualItem.size}px`,
                transform: `translateY(${virtualItem.start}px)`,
              }}
              data-index={virtualItem.index}
              ref={virtualizer.measureElement}
            >
              <button
                type="button"
                className={cn(
                  'flex h-full w-full cursor-pointer items-center gap-2 border-0 p-2 text-left hover:bg-accent hover:text-accent-foreground',
                  {
                    'bg-accent text-accent-foreground': isSelected,
                  },
                )}
                onClick={() => {
                  const countryName = country.name.common.toLowerCase();
                  onSelect(
                    countryName === selectedValue.toLowerCase()
                      ? ''
                      : countryName,
                  );
                }}
              >
                <div className="flex items-center justify-between gap-1">
                  <Image
                    src={country.flags.svg}
                    height={25}
                    width={25}
                    alt={`Flag of ${country.name.common}`}
                    loading="lazy"
                    className="flex-shrink-0"
                  />
                  <span className="truncate">
                    {country.name.common.toLowerCase()}
                  </span>
                </div>
              </button>
            </div>
          );
        })}
      </div>
    </div>
  );
};
