'use client';

import Link from 'next/link';
import { BadgePlus, Crown, LayoutGrid, LogOut, User2 } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Avatar } from '@/components/ui/avatar';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from '@/components/ui/tooltip';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { IUser } from '@/types/next-auth';
import { usePath } from '@/hooks/use-path';
import { signOut } from 'next-auth/react';

export function UserNav({ user }: { user: IUser }) {
  const path = usePath();
  return (
    <DropdownMenu>
      <TooltipProvider disableHoverableContent>
        <Tooltip delayDuration={100}>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                className="relative h-8 w-8 rounded-full"
              >
                <Avatar className="h-8 w-8">
                  {/*<AvatarImage src="#" alt="Avatar" />*/}
                  {/*<AvatarFallback className="bg-transparent">JD</AvatarFallback>*/}
                  <div
                    className={
                      'flex !h-8 w-fit cursor-pointer items-center justify-center !rounded-full border bg-gradient-to-r from-green-300 via-blue-500 to-purple-600 !p-2'
                    }
                  >
                    <User2 className="h-4 w-4 text-white" />
                  </div>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent side="bottom">Profile</TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{user.codePromo}</p>
            <p className="text-xs leading-none text-muted-foreground">
              {user.email}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem className="hover:cursor-pointer" asChild>
            <Link href="/dashboard/tcfhistory" className="flex items-center">
              <LayoutGrid className="mr-3 h-4 w-4 text-muted-foreground" />
              Tableau de bord
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem className="hover:cursor-pointer" asChild>
            <Link href="/checkout" className="flex items-center">
              <Crown className="mr-3 h-4 w-4 text-muted-foreground" />
              Payer un abonnement
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem className="hover:cursor-pointer" asChild>
            <Link
              href="/renew-expression-credits/client"
              className="flex items-center"
            >
              <BadgePlus className="mr-3 h-4 w-4 text-muted-foreground" />
              Recharge Expression
            </Link>
          </DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="hover:cursor-pointer"
          onClick={async () => {
            await signOut({
              redirect: true,
              callbackUrl: `/signin?callbackUrl=${path}`,
            });
          }}
        >
          <LogOut className="mr-3 h-4 w-4 text-muted-foreground" />
          Sign out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
