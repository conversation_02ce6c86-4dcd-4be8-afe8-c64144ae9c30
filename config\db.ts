export const TCFDBConfig = {
  name: 'TCF-EO',
  version: 1,
  objectStoresMeta: [
    {
      store: 'testeo',
      storeConfig: { keyPath: 'id', autoIncrement: true },
      storeSchema: [
        { name: 'key', keypath: 'key', options: { unique: true } },
        { name: 'payload', keypath: 'payload', options: { unique: false } },
      ],
    },
  ],
};

export const TEFDBConfig = {
  name: 'TEF-EO',
  version: 1,
  objectStoresMeta: [
    {
      store: 'testeo',
      storeConfig: { keyPath: 'id', autoIncrement: true },
      storeSchema: [
        { name: 'key', keypath: 'key', options: { unique: true } },
        { name: 'payload', keypath: 'payload', options: { unique: false } },
      ],
    },
  ],
};

export function deleteRecordByKey(
  dbName: string,
  storeName: string,
  keyValue: string,
): Promise<string> {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(dbName);

    request.onsuccess = function () {
      const db = request.result;
      const tx = db.transaction(storeName, 'readwrite');
      const store = tx.objectStore(storeName);
      const index = store.index('key'); // Use the indexed column "key"
      const query = index.getKey(keyValue); // Get the primary key (id) of the record

      query.onsuccess = function () {
        if (query.result) {
          store.delete(query.result); // Delete using the primary key
          resolve(`Deleted record where key = ${keyValue}`);
        } else {
          resolve(`No record found where key = ${keyValue}`);
        }
      };

      query.onerror = function () {
        reject('Error querying IndexedDB.');
      };
    };

    request.onerror = function () {
      reject('Error opening IndexedDB.');
    };
  });
}
