'use client';
import { createContext, PropsWithChildren, useContext, useState } from 'react';
import { createStore, StoreApi, useStore } from 'zustand';
export interface StoragePayload {
  currentTask: number;
  noteTaskOne: number | undefined;
  noteTaskTwo: number | undefined;
  noteTaskThree: number | undefined;
  commentTaskOne: string | undefined;
  commentTaskTwo: string | undefined;
  commentTaskThree: string | undefined;
}
interface CorrectionState {
  currentTask: number;
  noteTaskOne?: number;
  noteTaskTwo?: number;
  noteTaskThree?: number;
  commentTaskOne?: string;
  commentTaskTwo?: string;
  commentTaskThree?: string;
  serieId?: string;
  userId?: string;
  // Methods
  setCommentTask: (val: string) => void;
  setNoteTask: (val: number) => void;
  setCurrentTask: (val: number) => void;
  setSerieId: (val: string) => void;
  setUserId: (val: string) => void;
  // Reset all tasks and comments
  reset: () => void;

  uploadPreviousData: (val: StoragePayload) => void;
}

const CorrectionContext = createContext<StoreApi<CorrectionState> | undefined>(
  undefined,
);

type CorrectionProviderProps = PropsWithChildren & {
  initialCurrent: number;
};

import React from 'react';

export default function CorrectionProvider({
  children,
  initialCurrent,
}: CorrectionProviderProps) {
  const [store] = useState(() =>
    createStore<CorrectionState>((set) => ({
      currentTask: initialCurrent,
      uploadPreviousData: (val) =>
        set(() => ({
          currentTask: val.currentTask,
          noteTaskOne: val.noteTaskOne,
          noteTaskTwo: val.noteTaskTwo,
          noteTaskThree: val.noteTaskThree,
          commentTaskOne: val.commentTaskOne,
          commentTaskTwo: val.commentTaskTwo,
          commentTaskThree: val.commentTaskThree,
        })),
      setUserId: (val: string) =>
        set(() => ({
          userId: val,
        })),
      setCommentTask: (val: string) =>
        set((state) => {
          if (state.currentTask === 1) {
            return { commentTaskOne: val };
          } else if (state.currentTask === 2) {
            return { commentTaskTwo: val };
          } else if (state.currentTask === 3) {
            return { commentTaskThree: val };
          }
          return state;
        }),

      setSerieId: (val: string) =>
        set(() => ({
          serieId: val,
        })),
      setNoteTask: (val: number) =>
        set((state) => {
          if (state.currentTask === 1) {
            return { noteTaskOne: val };
          } else if (state.currentTask === 2) {
            return { noteTaskTwo: val };
          } else if (state.currentTask === 3) {
            return { noteTaskThree: val };
          }
          return state;
        }),
      setCurrentTask: (val: number) =>
        set(() => ({
          currentTask: val,
        })),
      reset: () =>
        set(() => ({
          currentTask: initialCurrent,
          noteTaskOne: undefined,
          noteTaskTwo: undefined,
          noteTaskThree: undefined,
          commentTaskOne: undefined,
          commentTaskTwo: undefined,
          commentTaskThree: undefined,
          serieId: undefined,
          userId: undefined,
        })),
    })),
  );
  return (
    <CorrectionContext.Provider value={store}>
      {children}
    </CorrectionContext.Provider>
  );
}

export function useCorrectionStore<T>(selector: (state: CorrectionState) => T) {
  const context = useContext(CorrectionContext);
  if (!context) {
    throw new Error('CorrectionContext.Provider is missing');
  }

  return useStore(context, selector);
}
