import { create } from 'zustand';

const MODE = {
  CE: 'CE',
  CO: 'CO',
  EE: 'EE',
  EO: 'EO',
} as const;

type ObjectValues<T> = T[keyof T];

export type DetailMode = ObjectValues<typeof MODE>;

interface DetailState {
  mode: DetailMode;
  setMode: (mode: DetailMode) => void;
}

export const useDetailState = create<DetailState>((set) => ({
  mode: 'CE',
  setMode: (mode) => set((state) => ({ mode: mode })),
}));
