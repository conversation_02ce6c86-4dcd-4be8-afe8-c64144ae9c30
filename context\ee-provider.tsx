'use client';
import { createContext, PropsWithChildren, useContext, useState } from 'react';
import { createStore, StoreApi, useStore } from 'zustand';

interface EOState {
  fileOne: File | null;
  fileTwo: File | null;
  fileThree: File | null;
  setFileOne: (val: File | null) => void;
  setFileTwo: (val: File | null) => void;
  setFileThree: (val: File | null) => void;
  prevFileOne: File | null;
  prevFileTwo: File | null;
  prevFileThree: File | null;
  prevSetFileOne: (val: File | null) => void;
  prevSetFileTwo: (val: File | null) => void;
  prevSetFileThree: (val: File | null) => void;
  reset: () => void;
  current: number;
  next: () => void;
}

const EOContext = createContext<StoreApi<EOState> | undefined>(undefined);

type EOProviderProps = PropsWithChildren & {
  initialCurrent: number;
};

import React from 'react';

export default function EOProvider({
  children,
  initialCurrent,
}: EOProviderProps) {
  const [store] = useState(() =>
    createStore<EOState>((set) => ({
      fileOne: null,
      fileThree: null,
      fileTwo: null,
      prevFileOne: null,
      prevFileThree: null,
      prevFileTwo: null,
      current: initialCurrent,
      setFileOne: (val) => set((state) => ({ fileOne: val })),
      setFileThree: (val) => set((state) => ({ fileThree: val })),
      setFileTwo: (val) => set((state) => ({ fileTwo: val })),
      prevSetFileOne: (val) => set((state) => ({ prevFileOne: val })),
      prevSetFileThree: (val) => set((state) => ({ prevFileThree: val })),
      prevSetFileTwo: (val) => set((state) => ({ prevFileTwo: val })),
      reset: () =>
        set(() => ({
          fileOne: null,
          fileThree: null,
          fileTwo: null,
          prevFileOne: null,
          prevFileThree: null,
          prevFileTwo: null,
          current: 1,
        })),
      next: () => set((state) => ({ current: state.current + 1 })),
    })),
  );
  return <EOContext.Provider value={store}>{children}</EOContext.Provider>;
}

export function useEOStore<T>(selector: (state: EOState) => T) {
  const context = useContext(EOContext);
  if (!context) {
    throw new Error('EOContext.Provider is missing');
  }

  return useStore(context, selector);
}
