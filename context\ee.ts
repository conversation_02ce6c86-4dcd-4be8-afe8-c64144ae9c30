import { create } from 'zustand';
import { useTestState } from './test';

interface EEState {
  textOne: string;
  setTextOne: (value: string) => void;
  textTwo: string;
  setTextTwo: (value: string) => void;
  textThree: string;
  setTextThree: (value: string) => void;
  resetEE: () => void;
}

export const useEEState = create<EEState>((set) => ({
  textOne: '',
  textThree: '',
  textTwo: '',
  setTextOne: (val) =>
    set(() => {
      const libelle = useTestState.getState().testSet?.libelle;
      const user = useTestState.getState().user;
      localStorage.setItem(
        `EE_RES_${libelle}_TASK_1_${user?._id}`,
        JSON.stringify({ text: val }),
      );
      return { textOne: val };
    }),
  setTextTwo: (val) =>
    set(() => {
      const libelle = useTestState.getState().testSet?.libelle;
      const user = useTestState.getState().user;
      localStorage.setItem(
        `EE_RES_${libelle}_TASK_2_${user?._id}`,
        JSON.stringify({ text: val }),
      );
      return { textTwo: val };
    }),
  setTextThree: (val) =>
    set(() => {
      const libelle = useTestState.getState().testSet?.libelle;
      const user = useTestState.getState().user;
      localStorage.setItem(
        `EE_RES_${libelle}_TASK_3_${user?._id}`,
        JSON.stringify({ text: val }),
      );
      return { textThree: val };
    }),
  resetEE: () => set(() => ({ textOne: '', textTwo: '', textThree: '' })),
}));
