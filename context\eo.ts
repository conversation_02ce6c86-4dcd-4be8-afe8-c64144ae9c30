import { create } from 'zustand';

interface EEState {
  fileOne: File | null;
  fileTwo: File | null;
  fileThree: File | null;
  setFileOne: (val: File | null) => void;
  setFileTwo: (val: File | null) => void;
  setFileThree: (val: File | null) => void;
  reset: () => void;
  current: number;
  next: () => void;
}

export const useEOState = create<EEState>((set) => ({
  fileOne: null,
  fileThree: null,
  fileTwo: null,
  current: 1,
  setFileOne: (val) => set((state) => ({ fileOne: val })),
  setFileThree: (val) => set((state) => ({ fileThree: val })),
  setFileTwo: (val) => set((state) => ({ fileTwo: val })),
  reset: () =>
    set(() => ({ fileOne: null, fileThree: null, fileTwo: null, current: 1 })),
  next: () => set((state) => ({ current: state.current + 1 })),
}));
