import { getScore, getTestSet } from '@/lib/utils';
import {
  ResultatResponse,
  Resultset,
  Row,
  ScoreC,
  Serie,
  TestSet,
} from '@/types';
import { create } from 'zustand';

export const MODE = {
  CE: 'CE',
  CO: 'CO',
  EE: 'EE',
  EO: 'EO',
  ALL: 'ALL',
} as const;

export type ObjectValues<T> = T[keyof T];

export type Mode = ObjectValues<typeof MODE>;

interface EOState {
  fileOne: File | null;
  fileTwo: File | null;
  fileThree: File | null;
  setFileOne: (val: File | null) => void;
  setFileTwo: (val: File | null) => void;
  setFileThree: (val: File | null) => void;
  reset: () => void;
  current: number;
  next: () => void;
}

interface EEState {
  textOne: string;
  setTextOne: (value: string) => void;
  textTwo: string;
  setTextTwo: (value: string) => void;
  textThree: string;
  setTextThree: (value: string) => void;
  resetEE: () => void;
}

interface FreeModeState {
  mode: Mode;
  isTart: boolean;
  isEnd: boolean;
  Start: () => void;
  Stop: () => void;
  SetMode: (value: Mode) => void;
  isFree: Boolean;
  setIsFree: (value: Boolean) => void;
  current: number;
  detail: ResultatResponse | undefined;
  setDetail: (val: ResultatResponse | undefined) => void;
  next: () => void;
  prev: () => void;
  setCurrent: (index: number) => void;
  resulSet: Resultset[];
  Addset: (value: Resultset) => void;
  Restart: () => void;
  testSet: TestSet | null;
  score: ScoreC | null;
  serie: Serie | null;
  selectedRow: Row | null;
  setSelectedRow: (value: Row) => void;
  setTestSet: (serie: Serie) => void;
  endtext: string;
  setEndtext: (val: string) => void;
}
export const useFreeModeState = create<FreeModeState>((set) => ({
  endtext: '',
  isTart: false,
  detail: undefined,
  isFree: true,
  isEnd: false,
  mode: 'CE',
  selectedRow: null,
  current: 0,
  testSet: null,
  serie: null,
  score: null,
  setEndtext: (val) => set(() => ({ endtext: val })),
  setSelectedRow: (value) => set((state) => ({ selectedRow: value })),
  Restart: () =>
    set((state) => {
      return {
        isEnd: false,
        isTart: false,
        resulSet: [],
        current: 0,
        testSet: null,
        detailId: '',
        score: null,
        serie: null,
      };
    }),
  setIsFree: (value) => set((state) => ({ isFree: value })),
  setDetail: (value) => set((state) => ({ detail: value })),
  next: () => set((state) => ({ current: state.current + 1 })),
  prev: () => set((state) => ({ current: state.current - 1 })),
  setCurrent: (index) => set((state) => ({ current: index })),
  Start: () => set((state) => ({ isTart: true, isEnd: false })),
  Stop: () =>
    set((state) => {
      const res = getScore(state.testSet!, state.resulSet);
      return { isEnd: true, isTart: false, score: res };
    }),
  SetMode: (value) => set((state) => ({ mode: value })),
  resulSet: [],
  Addset: (value) =>
    set((state) => {
      const tab = state.resulSet.filter(
        (res) => res.questionId !== value.questionId,
      );
      tab.push(value);
      return { resulSet: tab };
    }),
  setTestSet: (serie) =>
    set((state) => {
      const res = getTestSet(serie);
      return { testSet: res, serie: serie };
    }),
}));

export const useFreeEOState = create<EOState>((set) => ({
  fileOne: null,
  fileThree: null,
  fileTwo: null,
  current: 1,
  setFileOne: (val) => set((state) => ({ fileOne: val })),
  setFileThree: (val) => set((state) => ({ fileThree: val })),
  setFileTwo: (val) => set((state) => ({ fileTwo: val })),
  reset: () =>
    set(() => ({ fileOne: null, fileThree: null, fileTwo: null, current: 1 })),
  next: () => set((state) => ({ current: state.current + 1 })),
}));

export const useFreeEEState = create<EEState>((set) => ({
  textOne: '',
  textThree: '',
  textTwo: '',
  setTextOne: (val) => set(() => ({ textOne: val })),
  setTextTwo: (val) => set(() => ({ textTwo: val })),
  setTextThree: (val) => set(() => ({ textThree: val })),
  resetEE: () => set(() => ({ textOne: '', textTwo: '', textThree: '' })),
}));
