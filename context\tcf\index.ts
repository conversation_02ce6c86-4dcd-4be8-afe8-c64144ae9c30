import type { Resultset, Serie, TestSet } from '@/types';
import type { User } from 'next-auth';
import { create } from 'zustand';

export const MODE = {
  CE: 'CE',
  CO: 'CO',
  EE: 'EE',
  EO: 'EO',
  ALL: 'ALL',
} as const;

export type ObjectValues<T> = T[keyof T];

export type Mode = ObjectValues<typeof MODE>;

interface TestState {
  mode: Mode;
  SetMode: (value: Mode) => void;
  current: number;
  next: () => void;
  prev: () => void;
  setCurrent: (index: number) => void;
  resulSet: Resultset[];
  Addset: (value: Resultset) => void;
  setresSet: (value: Resultset[]) => void;
  Restart: () => void;
  testSet: TestSet | null;
  serie: Serie | null;
  setSerie: (serie: Serie) => void;
  user: User | null;
  setUser: (user: User | null) => void;
}
export const useTCFState = create<TestState>((set) => ({
  // Définissez votre état initial ici
  mode: 'CE',
  current: 0,
  testSet: null,
  serie: null,
  user: null,
  setUser: (user) => set(() => ({ user: user })),
  Restart: () =>
    set((state) => {
      return {
        resulSet: [],
        current: 0,
        serie: null,
        user: null,
      };
    }),
  next: () => set((state) => ({ current: state.current + 1 })),
  prev: () => set((state) => ({ current: state.current - 1 })),
  setCurrent: (index) => set((state) => ({ current: index })),
  setSerie: (serie) => set(() => ({ serie: serie })),
  SetMode: (value) => set(() => ({ mode: value })),
  resulSet: [],
  Addset: (value) =>
    set((state) => {
      const RES_NAME = `${state.mode}_RES_${state.serie?.libelle}_${state.user?._id}`;
      const tab = state.resulSet.filter(
        (res) => res.questionId !== value.questionId,
      );
      tab.push(value);
      localStorage.setItem(RES_NAME, JSON.stringify(tab));
      return { resulSet: tab };
    }),

  setresSet: (value) => {
    set(() => {
      return { resulSet: value };
    });
  },
}));

interface EEState {
  textOne: string;
  setTextOne: (value: string) => void;
  textTwo: string;
  setTextTwo: (value: string) => void;
  textThree: string;
  setTextThree: (value: string) => void;
  resetEE: () => void;
  LOCAL_TEXT_1?: string;
  LOCAL_TEXT_2?: string;
  LOCAL_TEXT_3?: string;
  setLocalVariables: (values: Partial<EEState>) => void;
}

export const useEEState = create<EEState>((set) => ({
  LOCAL_TEXT_1: undefined,
  LOCAL_TEXT_2: undefined,
  LOCAL_TEXT_3: undefined,
  setLocalVariables: (values) =>
    set((state) => ({
      LOCAL_TEXT_1:
        values.LOCAL_TEXT_1 !== undefined
          ? values.LOCAL_TEXT_1
          : state.LOCAL_TEXT_1,
      LOCAL_TEXT_2:
        values.LOCAL_TEXT_2 !== undefined
          ? values.LOCAL_TEXT_2
          : state.LOCAL_TEXT_2,
      LOCAL_TEXT_3:
        values.LOCAL_TEXT_3 !== undefined
          ? values.LOCAL_TEXT_3
          : state.LOCAL_TEXT_3,
    })),
  textOne: '',
  textThree: '',
  textTwo: '',
  setTextOne: (val) =>
    set((state) => {
      if (state.LOCAL_TEXT_1) {
        localStorage.setItem(state.LOCAL_TEXT_1, JSON.stringify({ text: val }));
      }
      return { textOne: val };
    }),
  setTextTwo: (val) =>
    set((state) => {
      if (state.LOCAL_TEXT_2) {
        localStorage.setItem(state.LOCAL_TEXT_2, JSON.stringify({ text: val }));
      }
      return { textTwo: val };
    }),
  setTextThree: (val) =>
    set((state) => {
      if (state.LOCAL_TEXT_3) {
        localStorage.setItem(state.LOCAL_TEXT_3, JSON.stringify({ text: val }));
      }
      return { textThree: val };
    }),
  resetEE: () =>
    set(() => ({
      textOne: '',
      textTwo: '',
      textThree: '',
      LOCAL_TEXT_1: undefined,
      LOCAL_TEXT_2: undefined,
      LOCAL_TEXT_3: undefined,
    })),
}));
