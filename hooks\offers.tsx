import API from '@/lib/axios';
import { SubscriptionPack } from '@/types';
import { useQuery } from '@tanstack/react-query';

export const useDealerOffers = () => {
  return useQuery({
    queryKey: ['dealer-offers'],
    queryFn: async () => {
      const res = await API.get<SubscriptionPack[]>('/api/offer/offers/dealer');
      return res.data;
    },
  });
};

export const useClientOffers = () => {
  return useQuery({
    queryKey: ['client-offers'],
    queryFn: async () => {
      const res = await API.get<SubscriptionPack[]>('/api/offer/offers/user');
      return res.data;
    },
  });
};
