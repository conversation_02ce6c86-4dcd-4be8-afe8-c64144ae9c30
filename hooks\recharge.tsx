import API from '@/lib/axios';
import { SubscriptionPack } from '@/types';
import { useQuery } from '@tanstack/react-query';

export const useDealerRecharge = () =>
  useQuery({
    queryKey: ['dealer-recharges'],
    queryFn: async () => {
      const res = await API.get<SubscriptionPack[]>(
        '/api/offer/recharges/dealer',
      );
      return res.data;
    },
  });

export const useUserRecharge = () =>
  useQuery({
    queryKey: ['user-recharges'],
    queryFn: async () => {
      const res = await API.get<SubscriptionPack[]>(
        '/api/offer/recharges/user',
      );
      return res.data;
    },
  });
