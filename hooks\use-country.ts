import { COUNTRIES } from '@/constants/countries';
import { useSession } from 'next-auth/react';
import { useMemo } from 'react';

export const useCountry = () => {
  const { data: session } = useSession();
  const userCountry =
    session?.user.pays.toLowerCase() === 'cameroun'
      ? 'cameroon'
      : session?.user.pays;

  const checkCountry = useMemo(() => {
    const country = COUNTRIES.find(
      (c) => c.name.common.toLowerCase() == userCountry?.toLowerCase(),
    );

    const region = country?.region;
    const subregion = country?.subregion;
    const alpha3Code = country?.cca3;

    if (alpha3Code === 'CMR') {
      return 'CMR';
    } else if (region === 'Africa' && subregion === 'Western Africa') {
      return 'AFO';
    } else {
      return 'OTHER';
    }
  }, [userCountry]);

  const dealerContry = useMemo(() => {
    const country = COUNTRIES.find(
      (c) => c.name.common.toLowerCase() == userCountry?.toLowerCase(),
    );

    const region = country?.region;
    const subregion = country?.subregion;
    const alpha3Code = country?.cca3;

    if (alpha3Code === 'CMR') {
      return 'cameroun';
    } else if (region === 'Africa' && subregion === 'Western Africa') {
      return 'afrique_ouest';
    } else {
      return 'international';
    }
  }, [userCountry]);
  return { checkCountry, dealerContry } as const;
};
