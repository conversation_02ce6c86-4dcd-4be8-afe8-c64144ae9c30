import SoldeNotification from '@/app/examen/_components/soldeNotification';
import { getUser } from '@/app/examen/utils';
import { getDayCount } from '@/lib/getDayCount';
import { useSession } from 'next-auth/react';
import { toast } from 'sonner';
type Examen = 'TEF' | 'TCF';
type Discipline = 'EE' | 'EO';

const getDiscipline = (examen: Examen, discipline: Discipline): string => {
  if (examen == 'TCF') {
    switch (discipline) {
      case 'EE':
        return 'Expression écrite';
      case 'EO':
        return 'Expression orale';
      default:
        return 'Unknow';
    }
  } else {
    switch (discipline) {
      case 'EE':
        return 'Production écrite';
      case 'EO':
        return 'Production orale';
      default:
        return 'Unknow';
    }
  }
};

function useCredits() {
  const { data: session } = useSession();
  const checkToast = async (type: Examen, discipline: Discipline) => {
    const user = await getUser(session!);
    let canToast = false;
    let needCheckOut = false;
    if (type == 'TCF' && discipline == 'EE') {
      if (user.remains?.remainTCF && user.remains?.remainTCF?.balance_ee < 1) {
        canToast = true;
        needCheckOut = getDayCount(user.remains?.remainTCF.remain_day) < 1;
      }
    } else if (type == 'TCF' && discipline == 'EO') {
      if (user.remains?.remainTCF && user.remains?.remainTCF?.balance_eo < 1) {
        canToast = true;
        needCheckOut = getDayCount(user.remains?.remainTCF.remain_day) < 1;
      }
    } else if (type == 'TEF' && discipline == 'EE') {
      if (user.remains?.remainTEF && user.remains?.remainTEF?.balance_ee < 1) {
        canToast = true;
        needCheckOut = getDayCount(user.remains?.remainTEF.remain_day) < 1;
      }
    } else if (type == 'TEF' && discipline == 'EO') {
      if (user.remains?.remainTEF && user.remains?.remainTEF?.balance_eo < 1) {
        canToast = true;
        needCheckOut = getDayCount(user.remains?.remainTEF.remain_day) < 1;
      }
    }
    return { canToast, needCheckOut };
  };
  const checkCredit = async (type: Examen, discipline: Discipline) => {
    const res = await checkToast(type, discipline);
    if (res.canToast) {
      toast.custom(
        (id) => (
          <SoldeNotification
            isRemain={res.needCheckOut}
            id={id}
            discipline={getDiscipline(type, discipline)}
            type={discipline}
          />
        ),
        {
          position: 'bottom-left',
          closeButton: true,
          duration: 1000 * 60 * 2,
        },
      );
      return false;
    } else {
      return true;
    }
  };

  return { checkCredit };
}

export default useCredits;
