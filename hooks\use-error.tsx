import axios, { AxiosError } from 'axios';
import { useRouter } from 'next/navigation';

export function useHandleError() {
  const router = useRouter();

  function handleError(error: unknown) {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;

      if (
        axiosError.response &&
        axiosError.response.data &&
        typeof axiosError.response.data === 'object' &&
        'message' in axiosError.response.data
      ) {
        const message = (axiosError.response.data as { message: string })
          .message;

        if (message === 'Invalid or Expired Token provided !') {
          router.push('/auth');
        }
      }
    }
  }

  return { handleError };
}
