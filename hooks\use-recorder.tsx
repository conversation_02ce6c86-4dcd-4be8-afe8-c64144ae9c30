'use client';
import { useRef, useState, RefObject, useEffect } from 'react';

type StatusType = 'idle' | 'recording' | 'paused';
export const useRecorder = () => {
  const [blob, setBlob] = useState<Blob | null>(null);
  const [blobUrl, setBlobUrl] = useState<string | null>(null);
  const [status, setStatus] = useState<StatusType>('idle');
  const [recoder, setrecoder] = useState<MediaRecorder | null>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const videRef = useRef() as RefObject<HTMLVideoElement>;

  useEffect(() => {
    if (status === 'recording' && stream && videRef.current) {
      videRef.current.srcObject = stream;
    }
  }, [status, stream, videRef]);
  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true,
      });
      setStream(stream);
      const mediaRecorder = new MediaRecorder(stream);
      setrecoder(mediaRecorder);

      mediaRecorder.onstart = () => {
        if (videRef.current) videRef.current.play();
      };

      mediaRecorder.onpause = () => {
        if (videRef.current) videRef.current.pause();
      };
      mediaRecorder.onresume = () => {
        if (videRef.current) videRef.current.play();
      };

      const chunks: Blob[] = [];
      mediaRecorder.ondataavailable = (e) => {
        if (e.data.size > 0) chunks.push(e.data);
      };

      mediaRecorder.onstop = () => {
        if (videRef.current) videRef.current.pause();
        const blob = new Blob(chunks, { type: 'video/webm' });
        setBlob(blob);
        const bloburl = URL.createObjectURL(blob);
        setBlobUrl(bloburl);
        if (videRef.current) {
          videRef.current.srcObject = null;
          videRef.current.src = blobUrl!;
          videRef.current.play();
        }
      };

      mediaRecorder.start();
      setStatus('recording');
    } catch (error) {
      // console.log('error when getting access');
      // console.error(error);
    }
  };

  const stopRecording = () => {
    if (recoder && status !== 'idle') {
      recoder.stop();
      recoder.stream.getTracks().map((track) => track.stop());
      setrecoder(null);
      setStream(null);
      setStatus('idle');
    }
  };

  const pauseRecording = () => {
    if (recoder && status === 'recording') {
      recoder.pause();
      setStatus('paused');
    }
  };
  const resumeRecording = () => {
    if (recoder && status === 'recording') {
      recoder.resume();
      setStatus('recording');
    }
  };

  return {
    status,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    videRef,
  };
};
