import API from '@/lib/axios';
import { ResultatEE } from '@/types';
import { useMutation } from '@tanstack/react-query';
import { usePath } from './use-path';
interface props {
  result: any;
  mode: string;
  examen: string;
}
interface UseUpdateIsViewProps {
  onError?: (error?: unknown, variables?: props, cxt?: unknown) => void;
  onSuccess?: (data?: ResultatEE, variables?: props, cxt?: unknown) => void;
}
export const useUpdateIsView = ({
  onError,
  onSuccess,
}: UseUpdateIsViewProps) => {
  const path = usePath();
  return useMutation(
    async ({ result, mode, examen }: props): Promise<ResultatEE> => {
      const url =
        mode == 'EE'
          ? `/api/${examen}eeTest/tests/${result._id}`
          : `/api/${examen}eoTest/tests/${result._id}`;
      const { data } = await API.patch<ResultatEE>(
        url,
        { isView: true, resultat: result.resultat },
        {
          headers: {
            'Current-Path': path,
          },
        },
      );
      return data;
    },
    {
      onError,
      onSuccess,
    },
  );
};
