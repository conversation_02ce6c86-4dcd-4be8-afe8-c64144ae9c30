import API from '@/lib/axios';
import { Profile } from '@/types';
import { useQuery } from '@tanstack/react-query';
export const useGetProfilesQuery = ({ isEnable }: { isEnable: boolean }) => {
  return useQuery({
    queryKey: ['profiles'],
    queryFn: async () => {
      const res = await API.get<{ profiles: Profile[] }>(
        '/api/dealer/profiles',
      );
      return res.data.profiles;
    },
    enabled: isEnable,
  });
};
