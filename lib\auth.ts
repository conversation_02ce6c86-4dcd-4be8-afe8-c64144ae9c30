import axios from 'axios';
import { NextAuthOptions, getServerSession } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { catchNetWorkError } from './utils';
import { cache } from 'react';
import logger from './logger';
enum Role {
  client = 'client',
  admin = 'admin',
  dealer = 'dealer',
}

export const authOptions = {
  secret: process.env.NEXTAUTH_SECRET,
  session: {
    strategy: 'jwt',
    // maxAge: 30 * 24 * 60 * 60,  30 days
  },
  pages: {
    signIn: '/signin',
    error: '/signin',
    signOut: '/',
  },
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { type: 'email' },
        password: { type: 'password' },
      },
      async authorize(credentials) {
        const BaseURL =
          process.env.NEXT_PUBLIC_BASEURL_PROD ||
          process.env.NEXT_PUBLIC_BASEURL ||
          '';
        const email = credentials?.email;
        const password = credentials?.password;
        if (!email || !password) {
          throw new Error('Email et mot de passe sont requis');
        }
        try {
          // === Connexion pour un user standard ===
          const {
            data: { token },
          } = await axios.post(`${BaseURL}/api/auth/login`, {
            email,
            password,
          });

          const accessToken = token;
          const { data: userData } = await axios.get(
            `${BaseURL}/api/user/users/user-info`,
            {
              headers: {
                Authorization: `Bearer ${accessToken}`,
              },
            },
          );

          // console.log('User data:', userData);

          return { ...userData, accessToken };
        } catch (err) {
          logger.log('Error during authorization:', err as any);
          // Catch network errors and handle them
          catchNetWorkError(err);
          if (axios.isAxiosError(err)) {
            logger.log('Axios error:', err.response);
            if (err.response?.data.message === 'this user is also connect') {
              throw new Error(
                'Vous ne pouvez pas vous connecter sur plusieurs appareils en même temps.',
              );
            }
            throw new Error('Mot de passe ou email incorrect');
          }
          throw new Error('Erreur inconnue, veuillez réessayer');
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, trigger, session, user }) {
      if (trigger === 'update' && session) {
        return Promise.resolve({ ...token, ...session });
      }
      return Promise.resolve({ ...token, ...user });
    },
    async session({ session, token, user }) {
      session.user._id = token._id;
      session.user.id = token._id;
      session.user.email = token.email;
      session.user.phone = token.phone;
      session.user.role = token.role;
      session.user.accessToken = token.accessToken;
      session.user.accountIsCheck = token.accountIsCheck;
      session.user.codePromo = token.codePromo;
      session.user.createdAt = token.createdAt;
      session.user.solde = token.solde;
      session.user.remains = token.remains;
      session.user.remainsDeals = token.remainsDeals;
      session.user.lastConnexion = token.lastConnexion;
      session.user.pays = token.pays;

      return Promise.resolve(session);
    },
  },
} satisfies NextAuthOptions;

export const getAuthSession = cache(async () => getServerSession(authOptions));
