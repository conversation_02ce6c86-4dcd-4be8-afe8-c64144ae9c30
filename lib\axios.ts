import { toast } from '@/components/ui/use-toast';
import { BASEURL } from '@/config';
import axios from 'axios';
import { getSession, signOut } from 'next-auth/react';
const API = axios.create({
  baseURL: BASEURL,
  timeout: 14000,
});

API.interceptors.request.use(async (config) => {
  const session = await getSession();
  const token = session?.user.accessToken;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  config.headers['Content-Type'] = 'application/json';
  config.headers.Accept = 'application/json';
  return config;
});

API.interceptors.response.use(
  function (response) {
    return response;
  },
  async function (error) {
    if (
      (error.response && error.response.status === 401) ||
      error.response.status == 403
    ) {
      const message = (error.response.data as { message: string }).message;
      if (message === 'Invalid or Expired Token provided !') {
        const path = error.config.headers['Current-Path'];
        await signOut({
          redirect: true,
          callbackUrl: `/auth?callbackUrl=${path || '/'}`,
        });
        toast({
          title: 'Déconnection! Vous êtes connecté sur 1 autre appareil !',
          variant: 'destructive',
        });
      }
    }
    return Promise.reject(error);
  },
);
export default API;
