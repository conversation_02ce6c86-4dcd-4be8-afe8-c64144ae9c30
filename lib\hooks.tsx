import { usePath } from '@/hooks/use-path';
import { ResultatEE, Serie } from '@/types';
import { useQuery } from '@tanstack/react-query';
import { useSession } from 'next-auth/react';
import API from './axios';

const getEETCF = async (path: string) => {
  const { data } = await API.get<ResultatEE[]>(`/api/eeTest/tests/user-info`, {
    headers: {
      'Current-Path': path,
    },
  });
  return data;
};

const getEOTCF = async (path: string) => {
  const { data } = await API.get<ResultatEE[]>(`/api/eoTest/tests/user-info`, {
    headers: {
      'Current-Path': path,
    },
  });
  return data;
};

const getEETEF = async (path: string) => {
  const { data } = await API.get<ResultatEE<SerieTEF>[]>(
    `/api/tef/eeTest/tests/user-info`,
    {
      headers: {
        'Current-Path': path,
      },
    },
  );
  return data;
};

const getEOTEF = async (path: string) => {
  const { data } = await API.get<ResultatEE<SerieTEF>[]>(
    `/api/tef/eoTest/tests/user-info`,
    {
      headers: {
        'Current-Path': path,
      },
    },
  );
  return data;
};

export const useGetExpressionResult = () => {
  const { data: session } = useSession();
  const path = usePath();
  const { data, isLoading, refetch } = useQuery(
    ['notifs'],
    async (): Promise<
      (ResultatEE<any> & { type: string; examen: string })[]
    > => {
      const [dataE_TCF, dataO_TCF, dataE_TEF, dataO_TEF] = await Promise.all([
        getEETCF(path),
        getEOTCF(path),
        getEETEF(path),
        getEOTEF(path),
      ]);

      const newETCF = dataE_TCF
        .filter((res) => res.status == 'terminer' && res.isView == false)
        .map((d) => {
          return { ...d, type: 'EE', examen: 'TCF' };
        });
      const newOTCF = dataO_TCF
        .filter((res) => res.status == 'terminer' && res.isView == false)
        .map((d) => {
          return { ...d, type: 'EO', examen: 'TCF' };
        });

      const newETEF = dataE_TEF
        .filter((res) => res.status == 'terminer' && res.isView == false)
        .map((d) => {
          return { ...d, type: 'EE', examen: 'TEF' };
        });
      const newOTEF = dataO_TEF
        .filter((res) => res.status == 'terminer' && res.isView == false)
        .map((d) => {
          return { ...d, type: 'EO', examen: 'TEF' };
        });
      return [...newETCF, ...newOTCF, ...newETEF, ...newOTEF];
    },
    {
      retry: true,
      enabled:
        Boolean(session) &&
        !path.includes('signin') &&
        !path.includes('signup'),
      refetchOnWindowFocus: 'always',
      refetchInterval: 60 * 5 * 1000,
      onError: (error) => {
        // console.log(error);
      },
    },
  );

  return {
    data,
    isLoading,
    refetch,
  } as const;
};

export const useSerie = ({ libelle }: { libelle: string }) => {
  const path = usePath();
  return useQuery(
    ['serie', libelle],
    async (): Promise<Serie> => {
      const { data: serie } = await API.get<Serie>(
        `/api/serie/series/${libelle}`,
        {
          headers: {
            'Current-Path': path,
          },
        },
      );
      return serie;
    },
    {
      staleTime: 60 * 3 * 1000,
    },
  );
};
