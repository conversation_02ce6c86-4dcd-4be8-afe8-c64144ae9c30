/**
 * Custom logger wrapper that handles logging differently based on environment
 * - Development: logs to console
 * - Production: sends logs to Sentry (implementation placeholder)
 */

type LogLevel = 'log' | 'info' | 'warn' | 'error' | 'debug';

interface LogContext {
  [key: string]: any;
}

class Logger {
  private isDevelopment: boolean;

  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development';
  }

  /**
   * Log a general message
   */
  log(message: any, context?: LogContext): void {
    this.logMessage('log', message, context);
  }

  /**
   * Log an informational message
   */
  info(message: any, context?: LogContext): void {
    this.logMessage('info', message, context);
  }

  /**
   * Log a warning message
   */
  warn(message: any, context?: LogContext): void {
    this.logMessage('warn', message, context);
  }

  /**
   * Log an error message
   */
  error(message: string, error?: Error | unknown, context?: LogContext): void {
    const errorContext =
      error instanceof Error
        ? {
            ...context,
            error: {
              name: error.name,
              message: error.message,
              stack: error.stack,
            },
          }
        : { ...context, error };

    this.logMessage('error', message, errorContext);
  }

  /**
   * Log a debug message
   */
  debug(message: string, context?: LogContext): void {
    this.logMessage('debug', message, context);
  }

  /**
   * Internal method to handle the actual logging
   */
  private logMessage(
    level: LogLevel,
    message: string,
    context?: LogContext,
  ): void {
    if (this.isDevelopment) {
      this.logToConsole(level, message, context);
    } else {
      this.logToSentry(level, message, context);
    }
  }

  /**
   * Log to console (development environment)
   */
  private logToConsole(
    level: LogLevel,
    message: string,
    context?: LogContext,
  ): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;

    switch (level) {
      case 'log':
        console.log(logMessage, context || '');
        break;
      case 'info':
        console.info(logMessage, context || '');
        break;
      case 'warn':
        console.warn(logMessage, context || '');
        break;
      case 'error':
        console.error(logMessage, context || '');
        break;
      case 'debug':
        console.debug(logMessage, context || '');
        break;
    }
  }

  /**
   * Log to Sentry (production environment)
   * TODO: Implement Sentry integration
   */
  private logToSentry(
    level: LogLevel,
    message: string,
    context?: LogContext,
  ): void {
    // Placeholder for Sentry implementation
    // This will be implemented later when Sentry is configured

    // Example of what the Sentry implementation might look like:
    /*
    import * as Sentry from '@sentry/nextjs';
    
    const sentryLevel = this.mapToSentryLevel(level);
    
    if (level === 'error' && context?.error) {
      Sentry.captureException(context.error, {
        tags: { level },
        extra: { message, ...context }
      });
    } else {
      Sentry.captureMessage(message, sentryLevel, {
        tags: { level },
        extra: context
      });
    }
    */

    // For now, we'll just store the log (could be sent to a logging service)
    const logEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      context,
      environment: 'production',
    };

    // In a real implementation, you might want to:
    // 1. Send to Sentry
    // 2. Send to another logging service
    // 3. Store in a database
    // 4. Send to a monitoring service

    // For now, we'll just suppress the logs in production
    // unless it's an error, in which case we might want to at least log to console
    if (level === 'error') {
      console.error(`[PRODUCTION ERROR] ${message}`, context);
    }
  }

  /**
   * Map our log levels to Sentry severity levels
   * This will be used when Sentry is implemented
   */
  private mapToSentryLevel(level: LogLevel): string {
    switch (level) {
      case 'debug':
        return 'debug';
      case 'log':
      case 'info':
        return 'info';
      case 'warn':
        return 'warning';
      case 'error':
        return 'error';
      default:
        return 'info';
    }
  }
}

// Create a singleton instance
const logger = new Logger();

// Export the logger instance
export default logger;

// Export individual methods for convenience
export const { log, info, warn, error, debug } = logger;
