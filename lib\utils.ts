import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import logger from '@/lib/logger';
import {
  EEPayload,
  EOPayload,
  LibelleSerie,
  Resultat,
  ResultatEE,
  ResultatResponse,
  Resultset,
  Row,
  ScoreC,
  Serie,
  TestSet,
  TestType,
} from '@/types';
import axios, { AxiosError } from 'axios';
import { Mode } from '@/context/test';
import {
  EO_TASK_ONE_TIME,
  EO_TASK_THREE_TIME,
  EO_TASK_TWO_TIME,
} from '@/config';
import API from './axios';
import { redirect } from 'next/navigation';
import SERVER_API from './axios.server';
import { Session } from 'next-auth';
import { getDayCount } from '@/lib/getDayCount';

const base =
  process.env.NEXT_PUBLIC_BASEURL_PROD || process.env.NEXT_PUBLIC_BASEURL || '';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const getNiveau = (score: number): string => {
  if (Number.isNaN(score)) {
    return '';
  } else {
    if (score >= 100 && score <= 199) {
      return 'A1';
    } else if (score >= 100 && score <= 299) {
      return 'A2';
    } else if (score >= 300 && score <= 399) {
      return 'B1';
    } else if (score >= 400 && score <= 499) {
      return 'B2';
    } else if (score >= 500 && score <= 599) {
      return 'C1';
    } else if (score >= 600 && score <= 699) {
      return 'C2';
    } else {
      return 'A1';
    }
  }
};

export const getScore = (test: TestSet, resulset: Resultset[]): ScoreC => {
  let CE: number | null = null;
  let CO: number | null = null;

  if (test.CE) {
    const response = resulset.filter(
      (q) => q.questionId >= 40 && q.questionId <= 79,
    );
    if (response.length !== 0) {
      CE = test.CE.reduce((acc, q) => {
        const check = response.find((res) => res.questionId == q.numero);
        if (check) {
          if (
            q.suggestions.find((suggestion) => suggestion._id == check.resId)
              ?.isCorrect
          ) {
            acc += getPoint(q.numero);
          }
        }
        return acc;
      }, 0);
    }
  }

  if (test.CO) {
    const response = resulset.filter(
      (q) => q.questionId >= 1 && q.questionId <= 39,
    );
    if (response.length !== 0) {
      CO = test.CO.reduce((acc, q) => {
        const check = response.find((res) => res.questionId == q.numero);
        if (check) {
          if (
            q.suggestions.find((suggestion) => suggestion._id == check.resId)
              ?.isCorrect
          ) {
            acc += getPoint(q.numero);
          }
        }
        return acc;
      }, 0);
    }
  }

  return { CE, CO };
};

export const getPoint = (num: number): number => {
  let res = 0;
  if (num < 40 && num > 0) {
    if (num < 5 && num > 0) {
      res = 3;
    } else if (num < 11 && num > 5) {
      res = 9;
    } else if (num < 20 && num > 10) {
      res = 15;
    } else if (num < 30 && num > 19) {
      res = 21;
    } else if (num < 36 && num > 29) {
      res = 26;
    } else if (num < 40 && num > 35) {
      res = 33;
    }
  } else {
    if (num < 44 && num > 39) {
      res = 3;
    } else if (num < 50 && num > 43) {
      res = 9;
    } else if (num < 59 && num > 44) {
      res = 15;
    } else if (num < 69 && num > 58) {
      res = 21;
    } else if (num < 75 && num > 68) {
      res = 26;
    } else if (num < 80 && num > 74) {
      res = 33;
    }
  }

  return res;
};

export const getScoreTEF = (
  serie: SerieTEF,
  resulset: Resultset[],
  mode: 'CE' | 'CO',
): number | null => {
  let score: number | null = null;
  if (mode == 'CE') {
    if (serie.ceQuestions.questions.length !== 0) {
      const response = resulset;
      if (response.length !== 0) {
        score = serie.ceQuestions.questions.reduce((acc, q) => {
          const check = response.filter((res) => res.questionId == q.numero);
          if (check.length == 1) {
            q.consignes.forEach((consigne) => {
              if (
                consigne.suggestions.find(
                  (suggestion) => suggestion._id == check[0].resId,
                )?.isCorrect
              ) {
                acc += getPointTEF(q.numero);
              }
            });
          } else {
            check.forEach((c) => {
              q.consignes.forEach((consigne) => {
                if (
                  consigne.suggestions.find(
                    (suggestion) => suggestion._id == c.resId,
                  )?.isCorrect
                ) {
                  acc += getPointTEF(q.numero);
                }
              });
            });
          }
          return acc;
        }, 0);
      }
    }
  } else {
    if (serie.coQuestions.questions.length !== 0) {
      const response = resulset;
      if (response.length !== 0) {
        score = serie.coQuestions.questions.reduce((acc, q) => {
          const check = response.filter((res) => res.questionId == q.numero);
          if (check.length == 1) {
            q.consignes.forEach((consigne) => {
              if (
                consigne.suggestions.find(
                  (suggestion) => suggestion._id == check[0].resId,
                )?.isCorrect
              ) {
                acc += getPointTEF(q.numero);
              }
            });
          } else {
            check.forEach((c) => {
              q.consignes.forEach((consigne) => {
                if (
                  consigne.suggestions.find(
                    (suggestion) => suggestion._id == c.resId,
                  )?.isCorrect
                ) {
                  acc += getPointTEF(q.numero);
                }
              });
            });
          }
          return acc;
        }, 0);
      }
    }
  }
  return score;
};

export const getPointTEF = (num: number): number => {
  let res = 0;
  if (num < 40 && num > 0) {
    if (num < 4 && num > 0) {
      res = 3;
    } else if (num < 9 && num > 3) {
      res = 5;
    } else if (num < 12 && num > 8) {
      res = 9;
    } else if (num < 21 && num > 11) {
      res = 15;
    } else if (num < 30 && num > 20) {
      res = 21;
    } else if (num < 37 && num > 29) {
      res = 26;
    } else if (num > 36) {
      res = 26;
    }
  }
  return res;
};

export const getTestSet = (serie: Serie): TestSet => {
  const res: TestSet = {
    serieId: serie._id,
    libelle: serie.libelle,
    CE: null,
    CO: null,
    EO: null,
    EE: null,
  };
  if (serie.questions) {
    res.CE = serie.questions
      .filter((q) => q.numero >= 40 && q.numero <= 79)
      .sort((a, b) => a.numero - b.numero);
    res.CO = serie.questions
      .filter((q) => q.numero >= 1 && q.numero <= 39)
      .sort((a, b) => a.numero - b.numero);
  }
  if (serie.eeQuestions) res.EE = serie.eeQuestions;
  if (serie.eoQuestions) res.EO = serie.eoQuestions;
  return res;
};

export const pushResult = async (
  resultat: Resultset[],
  userId: string,
  token: string,
  serieId: string,
  mode: 'CE' | 'CO',
): Promise<ResultatResponse | undefined> => {
  const data = {
    serie: serieId,
    user: userId,
    resultat: 0,
    payload: JSON.stringify(resultat, null, 0),
  };

  let urlpath = '';
  if (mode == 'CE') urlpath = 'TestCE/created';
  if (mode == 'CO') urlpath = 'TestCO/created';

  try {
    const { data: res } = await API.post<ResultatResponse>(
      `/api/${urlpath}`,
      data,
    );
    return res;
  } catch (e) {
    handleError(e);
    throw new Error("impossible d'enregister votre resultat", { cause: e });
  }
};

export const pushEEResult = async (data: any, token: string) => {
  try {
    const { data: res } = await API.post('/api/eeTest/created', data);
    return res;
  } catch (e) {
    if (e instanceof AxiosError) {
      logger.error('Error pushing EE result', e);
      if (
        e.code === 'ERR_NETWORK' ||
        e.code === 'ETIMEDOUT' ||
        e.code === 'ENOTFOUND' ||
        e.code === 'ECONNRESET'
      )
        throw new Error('Verifier votre connexion internet');
    }
    throw new Error("impossible d'enregister votre resultat");
  }
};

export const pushEEResultTEF = async (data: any, token: string) => {
  try {
    await axios.post(`${base}/api/tef/eeTest/created`, data, {
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
      },
    });
  } catch (e) {
    if (e instanceof AxiosError) {
      logger.error('Error in saveEETestUseCase', e);
      if (e.code === 'ERR_BAD_REQUEST')
        if (e.response?.data.message === 'this user is also connect') {
          throw new Error(
            'Vous ne pouvez pas être connecter sur plusieurs appareils',
          );
        } else {
          throw new Error('Format incorrect');
        }
      if (
        e.code === 'ERR_NETWORK' ||
        e.code === 'ETIMEDOUT' ||
        e.code === 'ENOTFOUND' ||
        e.code === 'ECONNRESET'
      )
        throw new Error('Verifier votre connexion internet');
    }
    throw new Error("impossible d'enregister votre resultat");
  }
};

export const pushEOResult = async (
  data: {
    user: string;
    serie: string;
    payload: string;
  },
  token: string,
) => {
  try {
    const { data: newRes } = await SERVER_API.post<ResultatEE>(
      `/api/eoTest/created`,
      data,
    );
    return newRes;
  } catch (e) {
    if (e instanceof AxiosError) {
      logger.error('Error in saveEOTestUseCase', e);
      if (e.code === 'ERR_BAD_REQUEST')
        if (e.response?.data.message === 'this user is also connect') {
          throw new Error(
            'Vous ne pouvez pas être connecter sur plusieurs appareils',
          );
        } else {
          throw new Error('Format incorrect');
        }
      if (
        e.code === 'ERR_NETWORK' ||
        e.code === 'ETIMEDOUT' ||
        e.code === 'ENOTFOUND' ||
        e.code === 'ECONNRESET'
      )
        throw new Error('Verifier votre connexion internet');
    }
    throw new Error("impossible d'enregister votre resultat");
  }
};

export const pushEOResultTEF = async (
  data: {
    user: string;
    serie: string;
    payload: string;
  },
  token: string,
) => {
  try {
    const { data: newRes } = await SERVER_API.post<ResultatEE>(
      `/api/tef/eoTest/created`,
      data,
    );
    return newRes;
  } catch (e) {
    if (e instanceof AxiosError) {
      logger.error('Error in saveTestCOUseCase', e);
      if (e.code === 'ERR_BAD_REQUEST')
        if (e.response?.data.message === 'this user is also connect') {
          throw new Error(
            'Vous ne pouvez pas être connecter sur plusieurs appareils',
          );
        } else {
          throw new Error('Format incorrect');
        }
      if (
        e.code === 'ERR_NETWORK' ||
        e.code === 'ETIMEDOUT' ||
        e.code === 'ENOTFOUND' ||
        e.code === 'ECONNRESET'
      )
        throw new Error('Verifier votre connexion internet');
    }
    throw new Error("impossible d'enregister votre resultat");
  }
};

export const getRow = (resultat: Resultat): Row => {
  const res: Row = {
    isEEview: true,
    isEOview: true,
    id: resultat._id,
    time: resultat.createdAt,
    serie: resultat.serie.libelle,
    MOY: '',
    CE: '-',
    CO: '-',
    EO: '-',
    EE: '-',
  };
  const resultSets = JSON.parse(resultat.payload) as Resultset[];
  // const score = getScore(getTestSet(resultat.serie), resultSets);
  // const { CE, CO } = score;

  // if (CE !== null) {
  //   res.CE = getNiveau(CE);
  // }
  // if (CO !== null) {
  //   res.CO = getNiveau(CO);
  // }

  if (isCO(resultSets)) {
    res.CO = getNiveau(resultat.resultat);
  } else {
    res.CE = getNiveau(resultat.resultat);
  }

  res.MOY = getNiveau(
    Math.min(...[resultat.resultat, -1].filter((x) => x > 0)),
  );

  return res;
};

export const getRowTEF = (resultat: Resultat, type: string): Row => {
  const res: Row = {
    isEEview: true,
    isEOview: true,
    id: resultat._id,
    time: resultat.createdAt,
    serie: resultat.serie.libelle,
    MOY: '',
    CE: '-',
    CO: '-',
    EO: '-',
    EE: '-',
  };

  if (type == 'CO') {
    res.CO = getNiveau(resultat.resultat);
  } else {
    res.CE = getNiveau(resultat.resultat);
  }

  res.MOY = getNiveau(
    Math.min(...[resultat.resultat, -1].filter((x) => x > 0)),
  );

  return res;
};

export const getOther = (
  prevResult: Resultat[] | undefined,
  current: Resultat | undefined,
) => {
  if (!prevResult || !current) {
    return undefined;
  }
  if (prevResult.length == 1) return prevResult.at(0);
  // first select all same serie an remove other
  prevResult = prevResult.filter((r) => r.serie._id === current.serie._id);
  if (prevResult.length == 0) {
    return current;
  }
  // determine what current is it? CO CE
  const paylod = JSON.parse(current.payload) as Resultset[];
  const CE = paylod.every((q) => q.questionId >= 40 && q.questionId <= 79);
  const CO = paylod.every((q) => q.questionId >= 1 && q.questionId <= 39);

  let prevWhitoutSame: Resultat[] = [];
  if (CE) {
    prevWhitoutSame = prevResult
      .filter((prev) => {
        return prev._id != current._id;
      })
      .sort((a, b) => {
        const d: any = new Date(a.createdAt);
        const c: any = new Date(b.createdAt);
        return c - d;
      });
  }

  if (CO) {
    prevWhitoutSame = prevResult
      .filter((prev) => {
        return prev._id != current._id;
      })
      .sort((a, b) => {
        const d: any = new Date(a.createdAt);
        const c: any = new Date(b.createdAt);
        return c - d;
      });
  }

  return prevWhitoutSame.at(0);
};

export const getExpressionNiveaau = (score: number): string => {
  let niveau = 'erreur';
  if (score >= 0 && score < 2) niveau = 'A1';
  if (score > 1 && score < 6) niveau = 'A2';
  if (score > 5 && score < 10) niveau = 'B1';
  if (score > 9 && score < 14) niveau = 'B2';
  if (score > 13 && score < 18) niveau = 'C1';
  if (score > 17 && score <= 20) niveau = 'C2';

  return niveau;
};

export const getEEtext = (num: number, payload: EEPayload) => {
  let text = '';
  switch (num) {
    case 79:
      text = payload.textOne;
      break;
    case 80:
      text = payload.textTwo;
      break;
    case 81:
      text = payload.textThree;
      break;

    default:
      break;
  }
  return text;
};

export const getEEtextTEF = (num: number, payload: EEPayload) => {
  let text = '';
  switch (num) {
    case 41:
      text = payload.textOne;
      break;
    case 42:
      text = payload.textTwo;
      break;
    default:
      break;
  }
  return text;
};
export const getEOtaskTime = (num: number, type: string): number => {
  let time = 0;
  switch (num) {
    case 82:
      time = type === 'TCF' ? EO_TASK_ONE_TIME : 60 * 5;
      break;
    case 83:
      time = type === 'TCF' ? EO_TASK_TWO_TIME : 60 * 10;
      break;
    case 84:
      time = EO_TASK_THREE_TIME;
      break;
    default:
      break;
  }
  return time;
};
export const getEOurl = (num: number, payload: EOPayload) => {
  let URL: string | null = null;
  switch (num) {
    case 82:
      URL = payload.taskUrl1;
      break;
    case 83:
      URL = payload.taskUrl2;
      break;
    case 84:
      URL = payload.taskUrl3;
      break;
    default:
      break;
  }
  return URL;
};

export const getEOurlTEF = (num: number, payload: EOPayload) => {
  let URL: string | null = null;
  switch (num) {
    case 41:
      URL = payload.taskUrl1;
      break;
    case 42:
      URL = payload.taskUrl2;
      break;
    default:
      break;
  }
  return URL;
};

export const getMaxNote = (libelle: string) => {
  let max = 0;
  switch (libelle) {
    case 'tache 1':
      max = 5;
      break;
    case 'tache 2':
      max = 8;
      break;
    case 'tache 3':
      max = 7;
      break;

    default:
      break;
  }
  return max;
};

export const getMaxNoteTEF = (libelle: string) => {
  let max = 0;
  switch (libelle) {
    case 'section A':
      max = 269;
      break;
    case 'section B':
      max = 430;
      break;
    default:
      break;
  }
  return max;
};
export const getTask = (
  mode: Mode,
  type: 'TCF' | 'TEF',
): { task: string; time: string } => {
  let res = {
    task: '',
    time: '',
  };
  switch (mode) {
    case 'CE':
      res.task = type == 'TCF' ? '39 questions' : 'plusieurs questions';
      res.time = '60 minutes';
      break;
    case 'CO':
      res.task = type == 'TCF' ? '39 questions' : 'plusieurs questions';
      res.time = type == 'TCF' ? '35 minutes' : '40 minutes';
      break;
    case 'EE':
      res.task = type == 'TCF' ? '3 tâches' : '2 SECTIONS';
      res.time = '60 minutes';
      break;
    case 'EO':
      res.task = type == 'TCF' ? '3 tâches' : '2 SECTIONS';
      res.time = type == 'TCF' ? '12 minutes' : '15 minutes';
      break;

    default:
      break;
  }

  return res;
};

export const isTestAvailable = (mode: Mode, serie: Serie): boolean => {
  let res = true;
  switch (mode) {
    case 'CE':
      res =
        serie.questions &&
        serie.questions?.filter((q) => q.numero >= 40 && q.numero <= 79)
          .length == 39;
      break;
    case 'CO':
      res =
        serie.questions &&
        serie.questions?.filter((q) => q.numero >= 1 && q.numero <= 39)
          .length == 39;
      break;
    case 'EE':
      res = serie.eeQuestions && serie?.eeQuestions[0]?.tasks.length == 3;
      break;
    case 'EO':
      res = serie.eoQuestions && serie?.eoQuestions[0]?.tasks.length == 3;
      break;
    case 'ALL':
    default:
      break;
  }
  return res;
};

export const isCO = (data: Resultset[]): boolean => {
  return data.some((q) => q.questionId >= 1 && q.questionId <= 39);
};

export const getTestType = (type: string | null): TestType => {
  switch (type) {
    case 'TCF':
      return 'TCF';
    case 'TEF':
      return 'TEF';
    default:
      return 'TCF';
  }
};

export function catchNetWorkError(error: unknown) {
  if (axios.isAxiosError(error)) {
    if (!error.response) {
      throw new Error('Verifier votre connexion internet');
    }
  }
}

export const getSeriesLibelles = async (type: 'TCF' | 'TEF') => {
  const { data } = await SERVER_API.get<LibelleSerie[]>(
    `/api${type === 'TEF' ? '/tef' : ''}/serie/series/libelles`,
  );
  return data
    .filter((s) => s.libelle !== '')
    .sort((a, b) => {
      return parseInt(a.libelle) - parseInt(b.libelle);
    });
};

export const getSeries = async () => {
  try {
    const { data } = await SERVER_API.get<string[]>(
      `/api/serie/series/libelles`,
    );
    return data;
  } catch (error) {
    catchNetWorkError(error);

    throw new Error('Une erreur est survenue .', { cause: error });
  }
};

export const getComprehensionResult = async (mode: 'CO' | 'CE') => {
  let urlpath = '';
  if (mode == 'CE') urlpath = 'TestCE/TestCEs';
  if (mode == 'CO') urlpath = 'TestCO/TestCOs';
  try {
    const { data } = await API.get<Resultat[]>(
      `/api/${urlpath}/user-info?populateSerie=true`,
    );

    return { data: data.filter((res) => res.serie !== null), error: false };
  } catch (error) {
    handleError(error);
  }
};

export const getExpressionResult = async (mode: 'EO' | 'EE') => {
  let urlpath = '';
  if (mode == 'EE') urlpath = 'eeTest/tests';
  if (mode == 'EO') urlpath = 'eoTest/tests';
  try {
    const { data } = await API.get<ResultatEE[]>(`/api/${urlpath}/user-info`);

    return { data: data.filter((res) => res.serie !== null), error: false };
  } catch (error) {
    handleError(error);
  }
};

export const getProfileComprehensionResult = async (
  mode: 'CO' | 'CE',
  profileId: string,
) => {
  let urlpath = '';
  if (mode == 'CE') urlpath = 'ce-tests';
  if (mode == 'CO') urlpath = 'co-tests';
  try {
    const { data } = await API.get(
      `/api/dealer/${urlpath}/user/${profileId}?populateSerie=true`,
    );

    const result = data.tests as Resultat[];

    return { data: result.filter((res) => res.serie !== null), error: false };
  } catch (error) {
    error;
  }
};

export const getProfileExpressionResult = async (
  mode: 'EO' | 'EE',
  profileId: string,
) => {
  let urlpath = '';
  if (mode == 'EE') urlpath = 'ee-tests';
  if (mode == 'EO') urlpath = 'eo-tests';
  try {
    const { data } = await API.get(`/api/dealer/${urlpath}/user/${profileId}`);
    const result = data.tests as ResultatEE[];
    return { data: result.filter((res) => res.serie !== null), error: false };
  } catch (error) {
    handleError(error);
  }
};

export const getComprehensionResultTEF = async (mode: 'CO' | 'CE') => {
  let urlpath = '';
  if (mode == 'CE') urlpath = 'tef/TestCE/TestCEs';
  if (mode == 'CO') urlpath = 'tef/TestCO/TestCOs';
  try {
    const { data } = await API.get<Resultat[]>(
      `/api/${urlpath}/user-info?populateSerie=true`,
    );

    return { data: data.filter((res) => res.serie !== null), error: false };
  } catch (error) {
    if (error instanceof AxiosError) {
      if (
        error.status == 404 &&
        error.response?.data.message ===
          `Aucun test ${mode} trouvé pour cet utilisateur.`
      ) {
        return { data: [], error: false };
      }
    }
    handleError(error);
  }
};

export const getExpressionResultTEF = async (mode: 'EO' | 'EE') => {
  let urlpath = '';
  if (mode == 'EE') urlpath = 'tef/eeTest/tests';
  if (mode == 'EO') urlpath = 'tef/eoTest/tests';
  try {
    const { data } = await API.get<ResultatEE[]>(`/api/${urlpath}/user-info`);

    return { data: data.filter((res) => res.serie !== null), error: false };
  } catch (error) {
    handleError(error);
  }
};

export function handleError(error: unknown) {
  catchNetWorkError(error);
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError;
    if (
      axiosError.response &&
      axiosError.response.data &&
      typeof axiosError.response.data === 'object' &&
      'message' in axiosError.response.data
    ) {
      const message = (axiosError.response.data as { message: string }).message;

      if (message === 'Invalid or Expired Token provided !') {
        logger.info('Redirecting to auth due to invalid token');
        redirect('/auth');
      }
    }
  }
}

export const getCanUse = (session: Session | null, exam: 'TCF' | 'TEF') => {
  let days: number = 0;
  switch (exam) {
    case 'TCF':
      days =
        session?.user.role == 'admin'
          ? 100
          : getDayCount(session?.user.remains?.remainTCF?.remain_day || null);
      break;
    case 'TEF':
      days =
        session?.user.role == 'admin'
          ? 100
          : getDayCount(session?.user.remains?.remainTEF?.remain_day || null);
      break;
    default:
      days = 0;
  }
  return days > 0;
};

export function getHumanReadableSize(file: File) {
  try {
    const sizeInBytes = file.size; // Taille en octets

    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let index = 0;

    let size = sizeInBytes;
    while (size >= 1024 && index < units.length - 1) {
      size /= 1024;
      index++;
    }

    return `${size.toFixed(2)} ${units[index]}`;
  } catch (err) {
    return null;
  }
}

export const getNumber = (number: number | null | undefined) => {
  if (!number) return 0;

  if (number < 0) return 0;

  return number;
};

export function isProfile(email: string | undefined | null): boolean {
  if (!email || typeof email !== 'string') return false;

  const parts = email.split('/');
  if (parts.length !== 2) return false;

  const [dealerEmail] = parts;

  // Expression régulière basique pour un email valide
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

  return emailRegex.test(dealerEmail);
}

export const getCurrency = (country: string) => {
  if (country === 'cameroun') {
    return 'F';
  } else if (country === 'afrique_ouest') {
    return 'XOF';
  } else {
    return 'EUR';
  }
};
