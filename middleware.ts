import { chain } from './middlewares/chain';
import { withAuth } from './middlewares/withAuth';
import { withMaintenance } from './middlewares/withMaintenance';
import { withPlan } from './middlewares/withPlan';

const middlewares = [withAuth, withPlan, withMaintenance];
export default chain(middlewares);
export const config = {
  matcher: [
    '/dashboard(.*)',
    '/output',
    '/detail(.*)',
    '/detailC(.*)',
    '/verify-checkout',
    '/notifications',
    '/methodologie(.*)',
    '/renew-expression-credits(.*)',
    '/email-verify',
    '/signin',
    '/signup',
    '/reset-password',
  ],
};
