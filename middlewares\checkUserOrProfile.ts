import { getToken } from 'next-auth/jwt';
import {
  NextFetchEvent,
  NextMiddleware,
  NextRequest,
  NextResponse,
} from 'next/server';

export function checkProfileOrUser(middleware: NextMiddleware) {
  return async (req: NextRequest, event: NextFetchEvent) => {
    const path = req.nextUrl.pathname;

    if (path.includes('/dashboard') && path.split('/').length < 4) {
      const token = await getToken({
        req,
        secret: process.env.NEXTAUTH_SECRET,
      });

      if (token?.type == 'profile') {
        return NextResponse.redirect(
          new URL(`/dashboard/profiles/${token.id}`, req.nextUrl),
        );
      }
    }

    return middleware(req, event);
  };
}
