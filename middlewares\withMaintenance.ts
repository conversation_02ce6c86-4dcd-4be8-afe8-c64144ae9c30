import {
  NextFetchEvent,
  NextMiddleware,
  NextRequest,
  NextResponse,
} from 'next/server';

const PUBLIC_FILE = /\.(.*)$/;
export function withMaintenance(middleware: NextMiddleware) {
  return async (req: NextRequest, event: NextFetchEvent) => {
    const { pathname } = req.nextUrl;
    const isMaintenance = process.env.MAINTENANCE_SITE;

    if (
      pathname.startsWith('/_next') || // exclude Next.js internals
      pathname.startsWith('/api') || //  exclude all API routes
      pathname.startsWith('/static') || // exclude static files
      PUBLIC_FILE.test(pathname) || // exclude all files in the public folder
      pathname.includes('maintenance')
    ) {
      return NextResponse.next();
    }
    if (isMaintenance == 'true') {
      return NextResponse.redirect(new URL(`/maintenance`, req.nextUrl));
    }
    return middleware(req, event);
  };
}
