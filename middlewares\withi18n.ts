import {
  NextFetchEvent,
  NextMiddleware,
  NextRequest,
  NextResponse,
} from 'next/server';
import acceptLanguage from 'accept-language';
import { fallbackLng, languages, cookieName } from '../app/i18n/setting';

acceptLanguage.languages(languages);
export function withi18n(middleware: NextMiddleware) {
  return async (req: NextRequest, event: NextFetchEvent) => {
    if (
      req.nextUrl.pathname.indexOf('icon') > -1 ||
      req.nextUrl.pathname.indexOf('chrome') > -1
    )
      return NextResponse.next();
    let lng;
    if (req.cookies.has(cookieName))
      lng = acceptLanguage.get(req?.cookies?.get(cookieName)!.value);
    if (!lng) lng = acceptLanguage.get(req.headers.get('Accept-Language'));
    if (!lng) lng = fallbackLng;

    // Redirect if lng in path is not supported

    if (
      !languages.some((loc) => req.nextUrl.pathname.startsWith(`/${loc}`)) &&
      !req.nextUrl.pathname.startsWith('/_next')
    ) {
      return NextResponse.redirect(
        new URL(`/${lng}${req.nextUrl.pathname}`, req.url),
      );
    }

    if (req.headers.has('referer')) {
      const refererUrl = new URL(req.headers.get('referer')!);
      const lngInReferer: string | undefined = languages.find((l) =>
        refererUrl.pathname.startsWith(`/${l}`),
      );
      const response = NextResponse.next();
      if (lngInReferer) response.cookies.set(cookieName, lngInReferer);
      return response;
    }
    return middleware(req, event);
  };
}
