const path = require('path');
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});
/** @type {import('next').NextConfig} */
const nextConfig = {
  compress: true,
  experimental: {
    missingSuspenseWithCSRBailout: false,
  },
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Ensure that all imports of 'yjs' resolve to the same instance
      config.resolve.alias['yjs'] = path.resolve(__dirname, 'node_modules/yjs');
    }
    return config;
  },
  images: {
    minimumCacheTTL: 2678400, // 31 jours en secondes
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'uploadthing.com',
      },
      {
        protocol: 'https',
        hostname: 'tlf-api-backup.onrender.com',
        pathname: '/api/question/file/**',
      },
      {
        protocol: 'https',
        hostname: 'abjectof-conoda2.onrender.com',
        pathname: '/api/question/file/**',
      },
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '3500',
        pathname: '/api/question/file/**',
      },
    ],
  },
};

module.exports = withBundleAnalyzer(nextConfig);
