import { CustomError } from 'ts-custom-error';
export class AuthError extends CustomError {
  constructor(message: string = 'Connectez vous!') {
    super(message);
    Object.defineProperty(this, 'name', { value: 'AuthError' });
  }
}

export class NetworkError extends CustomError {
  constructor() {
    super('Verifier votre connexion internet.');
    Object.defineProperty(this, 'name', { value: 'NetworkError' });
  }
}

export class TimeOutError extends Error {
  constructor() {
    super('La requete a pris trop de temps');
  }
}

export class NotNetworkError extends Error {
  constructor() {
    super('Verifier votre connexion internet.');
  }
}
