import type { DefaultUser } from 'next-auth';
export enum Role {
  client = 'client',
  admin = 'admin',
  dealer = 'dealer',
  superdealer = 'superdealer',
}

type Remain = {
  balance_ee: number;
  balance_eo: number;
  remain_day: string | null;
};

type RemainDeal = {
  balance_ee: number;
  balance_eo: number;
  remain_day: string | null;
  profilsLimits: number | null;
};

export interface IUser extends DefaultUser {
  role?: Role;
  _id: string;
  phone: string;
  pays: string;
  role: Role;
  remains:
    | {
        remainTCF: Remain | undefined;
        remainTEF: Remain | undefined;
      }
    | undefined;
  remainsDeals:
    | {
        remainTCF: RemainDeal | undefined;
        remainTEF: RemainDeal | undefined;
      }
    | undefined;
  accessToken: string;
  codePromo: string;
  solde: number | undefined;
  accountIsCheck: boolean | undefined;
  lastConnexion: string;
  createdAt: string;
}

declare module 'next-auth' {
  interface User extends IUser {}
  interface Session {
    user: User;
  }
}

export interface IUser {
  role?: Role;
  _id: string;
  email: string;
  phone: string;
  pays: string;
  role: Role;
  remains:
    | {
        remainTCF: Remain | undefined;
        remainTEF: Remain | undefined;
      }
    | undefined;
  remainsDeals:
    | {
        remainTCF: RemainDeal | undefined;
        remainTEF: RemainDeal | undefined;
      }
    | undefined;
  accessToken: string;
  codePromo: string | undefined;
  solde: number | undefined;
  accountIsCheck: boolean | undefined;
  lastConnexion: string;
  createdAt: string;
}

declare module 'next-auth/jwt' {
  interface JWT extends IUser {}
}
