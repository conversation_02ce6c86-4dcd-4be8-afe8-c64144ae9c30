declare module 'use-sound' {
  type SpriteMap = {
    [key: string]: [number, number];
  };
  type HookOptions<T = any> = T & {
    id?: string;
    volume?: number;
    playbackRate?: number;
    interrupt?: boolean;
    soundEnabled?: boolean;
    sprite?: SpriteMap;
    onload?: () => void;
  };
  interface PlayOptions {
    id?: string;
    forceSoundEnabled?: boolean;
    playbackRate?: number;
  }
  type PlayFunction = (options?: PlayOptions) => void;
  interface ExposedData {
    sound: Howl | null;
    stop: (id?: string) => void;
    pause: (id?: string) => void;
    duration: number | null;
  }
  type ReturnedValue = [PlayFunction, ExposedData];
  //  useSound = ()=><T = any>(src: string | string[], { id, volume, playbackRate, soundEnabled, interrupt, onload, ...delegated }?: HookOptions<T>)=> ReturnedValue;

  export default function useSound<T = any>(
    src: string | string[],
    {
      id,
      volume,
      playbackRate,
      soundEnabled,
      interrupt,
      onload,
      ...delegated
    }?: HookOptions<T>,
  ): ReturnedValue;
  export { useSound };
}
